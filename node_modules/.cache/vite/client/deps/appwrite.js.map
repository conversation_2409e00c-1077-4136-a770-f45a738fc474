{"version": 3, "sources": ["../../../../appwrite/node_modules/tslib/tslib.es6.js", "../../../../appwrite/src/query.ts", "../../../../appwrite/src/client.ts", "../../../../appwrite/src/service.ts", "../../../../appwrite/src/services/account.ts", "../../../../appwrite/src/services/avatars.ts", "../../../../appwrite/src/services/databases.ts", "../../../../appwrite/src/services/functions.ts", "../../../../appwrite/src/services/graphql.ts", "../../../../appwrite/src/services/locale.ts", "../../../../appwrite/src/services/messaging.ts", "../../../../appwrite/src/services/storage.ts", "../../../../appwrite/src/services/teams.ts", "../../../../appwrite/src/permission.ts", "../../../../appwrite/src/role.ts", "../../../../appwrite/src/id.ts", "../../../../appwrite/src/enums/authenticator-type.ts", "../../../../appwrite/src/enums/authentication-factor.ts", "../../../../appwrite/src/enums/o-auth-provider.ts", "../../../../appwrite/src/enums/browser.ts", "../../../../appwrite/src/enums/credit-card.ts", "../../../../appwrite/src/enums/flag.ts", "../../../../appwrite/src/enums/execution-method.ts", "../../../../appwrite/src/enums/image-gravity.ts", "../../../../appwrite/src/enums/image-format.ts"], "sourcesContent": ["/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    if (typeof b !== \"function\" && b !== null)\r\n        throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    var desc = Object.getOwnPropertyDescriptor(m, k);\r\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\r\n        desc = { enumerable: true, get: function() { return m[k]; } };\r\n    }\r\n    Object.defineProperty(o, k2, desc);\r\n}) : (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n});\r\n\r\nexport function __exportStar(m, o) {\r\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n}\r\n\r\nexport function __spreadArray(to, from, pack) {\r\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\r\n        if (ar || !(i in from)) {\r\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\r\n            ar[i] = from[i];\r\n        }\r\n    }\r\n    return to.concat(ar || Array.prototype.slice.call(from));\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nvar __setModuleDefault = Object.create ? (function(o, v) {\r\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\r\n}) : function(o, v) {\r\n    o[\"default\"] = v;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\r\n    __setModuleDefault(result, mod);\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\r\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\r\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\r\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\r\n}\r\n\r\nexport function __classPrivateFieldIn(state, receiver) {\r\n    if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\r\n    return typeof state === \"function\" ? receiver === state : state.has(receiver);\r\n}\r\n", "type QueryTypesSingle = string | number | boolean;\nexport type QueryTypesList = string[] | number[] | boolean[] | Query[];\nexport type QueryTypes = QueryTypesSingle | QueryTypesList;\ntype AttributesTypes = string | string[];\n\n/**\n * Helper class to generate query strings.\n */\nexport class Query {\n  method: string;\n  attribute: AttributesTypes | undefined;\n  values: QueryTypesList | undefined;\n\n  /**\n   * Constructor for Query class.\n   *\n   * @param {string} method\n   * @param {AttributesTypes} attribute\n   * @param {QueryTypes} values\n   */\n  constructor(\n    method: string,\n    attribute?: AttributesTypes,\n    values?: QueryTypes\n  ) {\n    this.method = method;\n    this.attribute = attribute;\n\n    if (values !== undefined) {\n      if (Array.isArray(values)) {\n        this.values = values;\n      } else {\n        this.values = [values] as QueryTypesList;\n      }\n    }\n  }\n\n  /**\n   * Convert the query object to a JSON string.\n   *\n   * @returns {string}\n   */\n  toString(): string {\n    return JSON.stringify({\n      method: this.method,\n      attribute: this.attribute,\n      values: this.values,\n    });\n  }\n\n  /**\n   * Filter resources where attribute is equal to value.\n   *\n   * @param {string} attribute\n   * @param {QueryTypes} value\n   * @returns {string}\n   */\n  static equal = (attribute: string, value: QueryTypes): string =>\n    new Query(\"equal\", attribute, value).toString();\n\n  /**\n   * Filter resources where attribute is not equal to value.\n   *\n   * @param {string} attribute\n   * @param {QueryTypes} value\n   * @returns {string}\n   */\n  static notEqual = (attribute: string, value: QueryTypes): string =>\n    new Query(\"notEqual\", attribute, value).toString();\n\n  /**\n   * Filter resources where attribute is less than value.\n   *\n   * @param {string} attribute\n   * @param {QueryTypes} value\n   * @returns {string}\n   */\n  static lessThan = (attribute: string, value: QueryTypes): string =>\n    new Query(\"lessThan\", attribute, value).toString();\n\n  /**\n   * Filter resources where attribute is less than or equal to value.\n   *\n   * @param {string} attribute\n   * @param {QueryTypes} value\n   * @returns {string}\n   */\n  static lessThanEqual = (attribute: string, value: QueryTypes): string =>\n    new Query(\"lessThanEqual\", attribute, value).toString();\n\n  /**\n   * Filter resources where attribute is greater than value.\n   *\n   * @param {string} attribute\n   * @param {QueryTypes} value\n   * @returns {string}\n   */\n  static greaterThan = (attribute: string, value: QueryTypes): string =>\n    new Query(\"greaterThan\", attribute, value).toString();\n\n  /**\n   * Filter resources where attribute is greater than or equal to value.\n   *\n   * @param {string} attribute\n   * @param {QueryTypes} value\n   * @returns {string}\n   */\n  static greaterThanEqual = (attribute: string, value: QueryTypes): string =>\n    new Query(\"greaterThanEqual\", attribute, value).toString();\n\n  /**\n   * Filter resources where attribute is null.\n   *\n   * @param {string} attribute\n   * @returns {string}\n   */\n  static isNull = (attribute: string): string =>\n    new Query(\"isNull\", attribute).toString();\n\n  /**\n   * Filter resources where attribute is not null.\n   *\n   * @param {string} attribute\n   * @returns {string}\n   */\n  static isNotNull = (attribute: string): string =>\n    new Query(\"isNotNull\", attribute).toString();\n\n  /**\n   * Filter resources where attribute is between start and end (inclusive).\n   *\n   * @param {string} attribute\n   * @param {string | number} start\n   * @param {string | number} end\n   * @returns {string}\n   */\n  static between = (attribute: string, start: string | number, end: string | number): string =>\n    new Query(\"between\", attribute, [start, end] as QueryTypesList).toString();\n\n  /**\n   * Filter resources where attribute starts with value.\n   *\n   * @param {string} attribute\n   * @param {string} value\n   * @returns {string}\n   */\n  static startsWith = (attribute: string, value: string): string =>\n    new Query(\"startsWith\", attribute, value).toString();\n\n  /**\n   * Filter resources where attribute ends with value.\n   *\n   * @param {string} attribute\n   * @param {string} value\n   * @returns {string}\n   */\n  static endsWith = (attribute: string, value: string): string =>\n    new Query(\"endsWith\", attribute, value).toString();\n\n  /**\n   * Specify which attributes should be returned by the API call.\n   *\n   * @param {string[]} attributes\n   * @returns {string}\n   */\n  static select = (attributes: string[]): string =>\n    new Query(\"select\", undefined, attributes).toString();\n\n  /**\n   * Filter resources by searching attribute for value.\n   * A fulltext index on attribute is required for this query to work.\n   *\n   * @param {string} attribute\n   * @param {string} value\n   * @returns {string}\n   */\n  static search = (attribute: string, value: string): string =>\n    new Query(\"search\", attribute, value).toString();\n\n  /**\n   * Sort results by attribute descending.\n   *\n   * @param {string} attribute\n   * @returns {string}\n   */\n  static orderDesc = (attribute: string): string =>\n    new Query(\"orderDesc\", attribute).toString();\n\n  /**\n   * Sort results by attribute ascending.\n   *\n   * @param {string} attribute\n   * @returns {string}\n   */\n  static orderAsc = (attribute: string): string =>\n    new Query(\"orderAsc\", attribute).toString();\n\n  /**\n   * Return results after documentId.\n   *\n   * @param {string} documentId\n   * @returns {string}\n   */\n  static cursorAfter = (documentId: string): string =>\n    new Query(\"cursorAfter\", undefined, documentId).toString();\n\n  /**\n   * Return results before documentId.\n   *\n   * @param {string} documentId\n   * @returns {string}\n   */\n  static cursorBefore = (documentId: string): string =>\n    new Query(\"cursorBefore\", undefined, documentId).toString();\n\n  /**\n   * Return only limit results.\n   *\n   * @param {number} limit\n   * @returns {string}\n   */\n  static limit = (limit: number): string =>\n    new Query(\"limit\", undefined, limit).toString();\n\n  /**\n   * Filter resources by skipping the first offset results.\n   *\n   * @param {number} offset\n   * @returns {string}\n   */\n  static offset = (offset: number): string =>\n    new Query(\"offset\", undefined, offset).toString();\n\n  /**\n   * Filter resources where attribute contains the specified value.\n   *\n   * @param {string} attribute\n   * @param {string | string[]} value\n   * @returns {string}\n   */\n  static contains = (attribute: string, value: string | string[]): string =>\n    new Query(\"contains\", attribute, value).toString();\n\n  /**\n   * Combine multiple queries using logical OR operator.\n   *\n   * @param {string[]} queries\n   * @returns {string}\n   */\n  static or = (queries: string[]) =>\n    new Query(\"or\", undefined, queries.map((query) => JSON.parse(query))).toString();\n\n  /**\n   * Combine multiple queries using logical AND operator.\n   *\n   * @param {string[]} queries\n   * @returns {string}\n   */\n  static and = (queries: string[]) =>\n    new Query(\"and\", undefined, queries.map((query) => JSON.parse(query))).toString();\n}\n", "import { Models } from './models';\n\n/**\n * Payload type representing a key-value pair with string keys and any values.\n */\ntype Payload = {\n    [key: string]: any;\n}\n\n/**\n * Headers type representing a key-value pair with string keys and string values.\n */\ntype Headers = {\n    [key: string]: string;\n}\n\n/**\n * Realtime response structure with different types.\n */\ntype RealtimeResponse = {\n    /**\n     * Type of the response: 'error', 'event', 'connected', 'response' or 'pong'.\n     */\n    type: 'error' | 'event' | 'connected' | 'response' | 'pong';\n\n    /**\n     * Data associated with the response based on the response type.\n     */\n    data: RealtimeResponseAuthenticated | RealtimeResponseConnected | RealtimeResponseError | RealtimeResponseEvent<unknown> | undefined;\n}\n\n/**\n * Realtime request structure for authentication.\n */\ntype RealtimeRequest = {\n    /**\n     * Type of the request: 'authentication'.\n     */\n    type: 'authentication';\n\n    /**\n     * Data required for authentication.\n     */\n    data: RealtimeRequestAuthenticate;\n}\n\n/**\n * Realtime event response structure with generic payload type.\n */\ntype RealtimeResponseEvent<T extends unknown> = {\n    /**\n     * List of event names associated with the response.\n     */\n    events: string[];\n\n    /**\n     * List of channel names associated with the response.\n     */\n    channels: string[];\n\n    /**\n     * Timestamp indicating the time of the event.\n     */\n    timestamp: number;\n\n    /**\n     * Payload containing event-specific data.\n     */\n    payload: T;\n}\n\n/**\n * Realtime response structure for errors.\n */\ntype RealtimeResponseError = {\n    /**\n     * Numeric error code indicating the type of error.\n     */\n    code: number;\n\n    /**\n     * Error message describing the encountered error.\n     */\n    message: string;\n}\n\n/**\n * Realtime response structure for a successful connection.\n */\ntype RealtimeResponseConnected = {\n    /**\n     * List of channels the user is connected to.\n     */\n    channels: string[];\n\n    /**\n     * User object representing the connected user (optional).\n     */\n    user?: object;\n}\n\n/**\n * Realtime response structure for authenticated connections.\n */\ntype RealtimeResponseAuthenticated = {\n    /**\n     * Destination channel for the response.\n     */\n    to: string;\n\n    /**\n     * Boolean indicating the success of the authentication process.\n     */\n    success: boolean;\n\n    /**\n     * User object representing the authenticated user.\n     */\n    user: object;\n}\n\n/**\n * Realtime request structure for authentication.\n */\ntype RealtimeRequestAuthenticate = {\n    /**\n     * Session identifier for authentication.\n     */\n    session: string;\n}\n\ntype TimeoutHandle = ReturnType<typeof setTimeout> | number;\n\n/**\n * Realtime interface representing the structure of a realtime communication object.\n */\ntype Realtime = {\n    /**\n     * WebSocket instance for realtime communication.\n     */\n    socket?: WebSocket;\n\n    /**\n     * Timeout for reconnect operations.\n     */\n    timeout?: TimeoutHandle;\n\n    /**\n     * Heartbeat interval for the realtime connection.\n    */\n    heartbeat?: TimeoutHandle;\n\n    /**\n     * URL for establishing the WebSocket connection.\n     */\n    url?: string;\n\n    /**\n     * Last received message from the realtime server.\n     */\n    lastMessage?: RealtimeResponse;\n\n    /**\n     * Set of channel names the client is subscribed to.\n     */\n    channels: Set<string>;\n\n    /**\n     * Map of subscriptions containing channel names and corresponding callback functions.\n     */\n    subscriptions: Map<number, {\n        channels: string[];\n        callback: (payload: RealtimeResponseEvent<any>) => void\n    }>;\n\n    /**\n     * Counter for managing subscriptions.\n     */\n    subscriptionsCounter: number;\n\n    /**\n     * Boolean indicating whether automatic reconnection is enabled.\n     */\n    reconnect: boolean;\n\n    /**\n     * Number of reconnection attempts made.\n     */\n    reconnectAttempts: number;\n\n    /**\n     * Function to get the timeout duration for communication operations.\n     */\n    getTimeout: () => number;\n\n    /**\n     * Function to establish a WebSocket connection.\n     */\n    connect: () => void;\n\n    /**\n     * Function to create a new WebSocket instance.\n     */\n    createSocket: () => void;\n\n    /**\n     * Function to create a new heartbeat interval.\n     */\n    createHeartbeat: () => void;\n\n    /**\n     * Function to clean up resources associated with specified channels.\n     *\n     * @param {string[]} channels - List of channel names to clean up.\n     */\n    cleanUp: (channels: string[]) => void;\n\n    /**\n     * Function to handle incoming messages from the WebSocket connection.\n     *\n     * @param {MessageEvent} event - Event containing the received message.\n     */\n    onMessage: (event: MessageEvent) => void;\n}\n\n/**\n * Type representing upload progress information.\n */\ntype UploadProgress = {\n    /**\n     * Identifier for the upload progress.\n     */\n    $id: string;\n\n    /**\n     * Current progress of the upload (in percentage).\n     */\n    progress: number;\n\n    /**\n     * Total size uploaded (in bytes) during the upload process.\n     */\n    sizeUploaded: number;\n\n    /**\n     * Total number of chunks that need to be uploaded.\n     */\n    chunksTotal: number;\n\n    /**\n     * Number of chunks that have been successfully uploaded.\n     */\n    chunksUploaded: number;\n}\n\n/**\n * Exception thrown by the  package\n */\nclass AppwriteException extends Error {\n    /**\n     * The error code associated with the exception.\n     */\n    code: number;\n\n    /**\n     * The response string associated with the exception.\n     */\n    response: string;\n\n    /**\n     * Error type.\n     * See [Error Types](https://appwrite.io/docs/response-codes#errorTypes) for more information.\n     */\n    type: string;\n\n    /**\n     * Initializes a Appwrite Exception.\n     *\n     * @param {string} message - The error message.\n     * @param {number} code - The error code. Default is 0.\n     * @param {string} type - The error type. Default is an empty string.\n     * @param {string} response - The response string. Default is an empty string.\n     */\n    constructor(message: string, code: number = 0, type: string = '', response: string = '') {\n        super(message);\n        this.name = 'AppwriteException';\n        this.message = message;\n        this.code = code;\n        this.type = type;\n        this.response = response;\n    }\n}\n\n/**\n * Client that handles requests to Appwrite\n */\nclass Client {\n    static CHUNK_SIZE = 1024 * 1024 * 5;\n\n    /**\n     * Holds configuration such as project.\n     */\n    config = {\n        endpoint: 'https://cloud.appwrite.io/v1',\n        endpointRealtime: '',\n        project: '',\n        jwt: '',\n        locale: '',\n        session: '',\n    };\n    /**\n     * Custom headers for API requests.\n     */\n    headers: Headers = {\n        'x-sdk-name': 'Web',\n        'x-sdk-platform': 'client',\n        'x-sdk-language': 'web',\n        'x-sdk-version': '17.0.0',\n        'X-Appwrite-Response-Format': '1.6.0',\n    };\n\n    /**\n     * Set Endpoint\n     *\n     * Your project endpoint\n     *\n     * @param {string} endpoint\n     *\n     * @returns {this}\n     */\n    setEndpoint(endpoint: string): this {\n        this.config.endpoint = endpoint;\n        this.config.endpointRealtime = this.config.endpointRealtime || this.config.endpoint.replace('https://', 'wss://').replace('http://', 'ws://');\n\n        return this;\n    }\n\n    /**\n     * Set Realtime Endpoint\n     *\n     * @param {string} endpointRealtime\n     *\n     * @returns {this}\n     */\n    setEndpointRealtime(endpointRealtime: string): this {\n        this.config.endpointRealtime = endpointRealtime;\n\n        return this;\n    }\n\n    /**\n     * Set Project\n     *\n     * Your project ID\n     *\n     * @param value string\n     *\n     * @return {this}\n     */\n    setProject(value: string): this {\n        this.headers['X-Appwrite-Project'] = value;\n        this.config.project = value;\n        return this;\n    }\n    /**\n     * Set JWT\n     *\n     * Your secret JSON Web Token\n     *\n     * @param value string\n     *\n     * @return {this}\n     */\n    setJWT(value: string): this {\n        this.headers['X-Appwrite-JWT'] = value;\n        this.config.jwt = value;\n        return this;\n    }\n    /**\n     * Set Locale\n     *\n     * @param value string\n     *\n     * @return {this}\n     */\n    setLocale(value: string): this {\n        this.headers['X-Appwrite-Locale'] = value;\n        this.config.locale = value;\n        return this;\n    }\n    /**\n     * Set Session\n     *\n     * The user session to authenticate with\n     *\n     * @param value string\n     *\n     * @return {this}\n     */\n    setSession(value: string): this {\n        this.headers['X-Appwrite-Session'] = value;\n        this.config.session = value;\n        return this;\n    }\n\n    private realtime: Realtime = {\n        socket: undefined,\n        timeout: undefined,\n        heartbeat: undefined,\n        url: '',\n        channels: new Set(),\n        subscriptions: new Map(),\n        subscriptionsCounter: 0,\n        reconnect: true,\n        reconnectAttempts: 0,\n        lastMessage: undefined,\n        connect: () => {\n            clearTimeout(this.realtime.timeout);\n            this.realtime.timeout = window?.setTimeout(() => {\n                this.realtime.createSocket();\n            }, 50);\n        },\n        getTimeout: () => {\n            switch (true) {\n                case this.realtime.reconnectAttempts < 5:\n                    return 1000;\n                case this.realtime.reconnectAttempts < 15:\n                    return 5000;\n                case this.realtime.reconnectAttempts < 100:\n                    return 10_000;\n                default:\n                    return 60_000;\n            }\n        },\n        createHeartbeat: () => {\n            if (this.realtime.heartbeat) {\n                clearTimeout(this.realtime.heartbeat);\n            }\n\n            this.realtime.heartbeat = window?.setInterval(() => {\n                this.realtime.socket?.send(JSON.stringify({\n                    type: 'ping'\n                }));\n            }, 20_000);\n        },\n        createSocket: () => {\n            if (this.realtime.channels.size < 1) {\n                this.realtime.reconnect = false;\n                this.realtime.socket?.close();\n                return;\n            }\n\n            const channels = new URLSearchParams();\n            channels.set('project', this.config.project);\n            this.realtime.channels.forEach(channel => {\n                channels.append('channels[]', channel);\n            });\n\n            const url = this.config.endpointRealtime + '/realtime?' + channels.toString();\n\n            if (\n                url !== this.realtime.url || // Check if URL is present\n                !this.realtime.socket || // Check if WebSocket has not been created\n                this.realtime.socket?.readyState > WebSocket.OPEN // Check if WebSocket is CLOSING (3) or CLOSED (4)\n            ) {\n                if (\n                    this.realtime.socket &&\n                    this.realtime.socket?.readyState < WebSocket.CLOSING // Close WebSocket if it is CONNECTING (0) or OPEN (1)\n                ) {\n                    this.realtime.reconnect = false;\n                    this.realtime.socket.close();\n                }\n\n                this.realtime.url = url;\n                this.realtime.socket = new WebSocket(url);\n                this.realtime.socket.addEventListener('message', this.realtime.onMessage);\n                this.realtime.socket.addEventListener('open', _event => {\n                    this.realtime.reconnectAttempts = 0;\n                    this.realtime.createHeartbeat();\n                });\n                this.realtime.socket.addEventListener('close', event => {\n                    if (\n                        !this.realtime.reconnect ||\n                        (\n                            this.realtime?.lastMessage?.type === 'error' && // Check if last message was of type error\n                            (<RealtimeResponseError>this.realtime?.lastMessage.data).code === 1008 // Check for policy violation 1008\n                        )\n                    ) {\n                        this.realtime.reconnect = true;\n                        return;\n                    }\n\n                    const timeout = this.realtime.getTimeout();\n                    console.error(`Realtime got disconnected. Reconnect will be attempted in ${timeout / 1000} seconds.`, event.reason);\n\n                    setTimeout(() => {\n                        this.realtime.reconnectAttempts++;\n                        this.realtime.createSocket();\n                    }, timeout);\n                })\n            }\n        },\n        onMessage: (event) => {\n            try {\n                const message: RealtimeResponse = JSON.parse(event.data);\n                this.realtime.lastMessage = message;\n                switch (message.type) {\n                    case 'connected':\n                        const cookie = JSON.parse(window.localStorage.getItem('cookieFallback') ?? '{}');\n                        const session = cookie?.[`a_session_${this.config.project}`];\n                        const messageData = <RealtimeResponseConnected>message.data;\n\n                        if (session && !messageData.user) {\n                            this.realtime.socket?.send(JSON.stringify(<RealtimeRequest>{\n                                type: 'authentication',\n                                data: {\n                                    session\n                                }\n                            }));\n                        }\n                        break;\n                    case 'event':\n                        let data = <RealtimeResponseEvent<unknown>>message.data;\n                        if (data?.channels) {\n                            const isSubscribed = data.channels.some(channel => this.realtime.channels.has(channel));\n                            if (!isSubscribed) return;\n                            this.realtime.subscriptions.forEach(subscription => {\n                                if (data.channels.some(channel => subscription.channels.includes(channel))) {\n                                    setTimeout(() => subscription.callback(data));\n                                }\n                            })\n                        }\n                        break;\n                    case 'pong':\n                        break; // Handle pong response if needed\n                    case 'error':\n                        throw message.data;\n                    default:\n                        break;\n                }\n            } catch (e) {\n                console.error(e);\n            }\n        },\n        cleanUp: channels => {\n            this.realtime.channels.forEach(channel => {\n                if (channels.includes(channel)) {\n                    let found = Array.from(this.realtime.subscriptions).some(([_key, subscription] )=> {\n                        return subscription.channels.includes(channel);\n                    })\n\n                    if (!found) {\n                        this.realtime.channels.delete(channel);\n                    }\n                }\n            })\n        }\n    }\n\n    /**\n     * Subscribes to Appwrite events and passes you the payload in realtime.\n     *\n     * @param {string|string[]} channels\n     * Channel to subscribe - pass a single channel as a string or multiple with an array of strings.\n     *\n     * Possible channels are:\n     * - account\n     * - collections\n     * - collections.[ID]\n     * - collections.[ID].documents\n     * - documents\n     * - documents.[ID]\n     * - files\n     * - files.[ID]\n     * - executions\n     * - executions.[ID]\n     * - functions.[ID]\n     * - teams\n     * - teams.[ID]\n     * - memberships\n     * - memberships.[ID]\n     * @param {(payload: RealtimeMessage) => void} callback Is called on every realtime update.\n     * @returns {() => void} Unsubscribes from events.\n     */\n    subscribe<T extends unknown>(channels: string | string[], callback: (payload: RealtimeResponseEvent<T>) => void): () => void {\n        let channelArray = typeof channels === 'string' ? [channels] : channels;\n        channelArray.forEach(channel => this.realtime.channels.add(channel));\n\n        const counter = this.realtime.subscriptionsCounter++;\n        this.realtime.subscriptions.set(counter, {\n            channels: channelArray,\n            callback\n        });\n\n        this.realtime.connect();\n\n        return () => {\n            this.realtime.subscriptions.delete(counter);\n            this.realtime.cleanUp(channelArray);\n            this.realtime.connect();\n        }\n    }\n\n    prepareRequest(method: string, url: URL, headers: Headers = {}, params: Payload = {}): { uri: string, options: RequestInit } {\n        method = method.toUpperCase();\n\n        headers = Object.assign({}, this.headers, headers);\n\n        if (typeof window !== 'undefined' && window.localStorage) {\n            const cookieFallback = window.localStorage.getItem('cookieFallback');\n            if (cookieFallback) {\n                headers['X-Fallback-Cookies'] = cookieFallback;\n            }\n        }\n\n        let options: RequestInit = {\n            method,\n            headers,\n            credentials: 'include',\n        };\n\n        if (method === 'GET') {\n            for (const [key, value] of Object.entries(Client.flatten(params))) {\n                url.searchParams.append(key, value);\n            }\n        } else {\n            switch (headers['content-type']) {\n                case 'application/json':\n                    options.body = JSON.stringify(params);\n                    break;\n\n                case 'multipart/form-data':\n                    const formData = new FormData();\n\n                    for (const [key, value] of Object.entries(params)) {\n                        if (value instanceof File) {\n                            formData.append(key, value, value.name);\n                        } else if (Array.isArray(value)) {\n                            for (const nestedValue of value) {\n                                formData.append(`${key}[]`, nestedValue);\n                            }\n                        } else {\n                            formData.append(key, value);\n                        }\n                    }\n\n                    options.body = formData;\n                    delete headers['content-type'];\n                    break;\n            }\n        }\n\n        return { uri: url.toString(), options };\n    }\n\n    async chunkedUpload(method: string, url: URL, headers: Headers = {}, originalPayload: Payload = {}, onProgress: (progress: UploadProgress) => void) {\n        const file = Object.values(originalPayload).find((value) => value instanceof File);\n\n        if (file.size <= Client.CHUNK_SIZE) {\n            return await this.call(method, url, headers, originalPayload);\n        }\n\n        let start = 0;\n        let response = null;\n\n        while (start < file.size) {\n            let end = start + Client.CHUNK_SIZE; // Prepare end for the next chunk\n            if (end >= file.size) {\n                end = file.size; // Adjust for the last chunk to include the last byte\n            }\n\n            headers['content-range'] = `bytes ${start}-${end-1}/${file.size}`;\n            const chunk = file.slice(start, end);\n\n            let payload = { ...originalPayload, file: new File([chunk], file.name)};\n\n            response = await this.call(method, url, headers, payload);\n\n            if (onProgress && typeof onProgress === 'function') {\n                onProgress({\n                    $id: response.$id,\n                    progress: Math.round((end / file.size) * 100),\n                    sizeUploaded: end,\n                    chunksTotal: Math.ceil(file.size / Client.CHUNK_SIZE),\n                    chunksUploaded: Math.ceil(end / Client.CHUNK_SIZE)\n                });\n            }\n\n            if (response && response.$id) {\n                headers['x-appwrite-id'] = response.$id;\n            }\n\n            start = end;\n        }\n\n        return response;\n    }\n\n    async ping(): Promise<string> {\n        return this.call('GET', new URL(this.config.endpoint + '/ping'));\n    }\n\n    async call(method: string, url: URL, headers: Headers = {}, params: Payload = {}, responseType = 'json'): Promise<any> {\n        const { uri, options } = this.prepareRequest(method, url, headers, params);\n\n        let data: any = null;\n\n        const response = await fetch(uri, options);\n\n        const warnings = response.headers.get('x-appwrite-warning');\n        if (warnings) {\n            warnings.split(';').forEach((warning: string) => console.warn('Warning: ' + warning));\n        }\n\n        if (response.headers.get('content-type')?.includes('application/json')) {\n            data = await response.json();\n        } else if (responseType === 'arrayBuffer') {\n            data = await response.arrayBuffer();\n        } else {\n            data = {\n                message: await response.text()\n            };\n        }\n\n        if (400 <= response.status) {\n            throw new AppwriteException(data?.message, response.status, data?.type, data);\n        }\n\n        const cookieFallback = response.headers.get('X-Fallback-Cookies');\n\n        if (typeof window !== 'undefined' && window.localStorage && cookieFallback) {\n            window.console.warn('Appwrite is using localStorage for session management. Increase your security by adding a custom domain as your API endpoint.');\n            window.localStorage.setItem('cookieFallback', cookieFallback);\n        }\n\n        return data;\n    }\n\n    static flatten(data: Payload, prefix = ''): Payload {\n        let output: Payload = {};\n\n        for (const [key, value] of Object.entries(data)) {\n            let finalKey = prefix ? prefix + '[' + key +']' : key;\n            if (Array.isArray(value)) {\n                output = { ...output, ...Client.flatten(value, finalKey) };\n            } else {\n                output[finalKey] = value;\n            }\n        }\n\n        return output;\n    }\n}\n\nexport { Client, AppwriteException };\nexport { Query } from './query';\nexport type { Models, Payload, UploadProgress };\nexport type { RealtimeResponseEvent };\nexport type { QueryTypes, QueryTypesList } from './query';\n", "import { Client } from './client';\nimport type { Payload } from './client';\n\nexport class Service {\n    /**\n     * The size for chunked uploads in bytes.\n     */\n    static CHUNK_SIZE = 5*1024*1024; // 5MB\n\n    client: Client;\n\n    constructor(client: Client) {\n        this.client = client;\n    }\n\n    static flatten(data: Payload, prefix = ''): Payload {\n        let output: Payload = {};\n\n        for (const [key, value] of Object.entries(data)) {\n            let finalKey = prefix ? prefix + '[' + key +']' : key;\n            if (Array.isArray(value)) {\n                output = { ...output, ...Service.flatten(value, finalKey) };\n            } else {\n                output[finalKey] = value;\n            }\n        }\n\n        return output;\n    }\n}", "import { Service } from '../service';\nimport { AppwriteException, Client, type Payload, UploadProgress } from '../client';\nimport type { Models } from '../models';\nimport { AuthenticatorType } from '../enums/authenticator-type';\nimport { AuthenticationFactor } from '../enums/authentication-factor';\nimport { OAuthProvider } from '../enums/o-auth-provider';\n\nexport class Account {\n    client: Client;\n\n    constructor(client: Client) {\n        this.client = client;\n    }\n\n    /**\n     * Get account\n     *\n     * Get the currently logged in user.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.User<Preferences>>}\n     */\n    async get<Preferences extends Models.Preferences>(): Promise<Models.User<Preferences>> {\n        const apiPath = '/account';\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * Create account\n     *\n     * Use this endpoint to allow a new user to register a new account in your project. After the user registration completes successfully, you can use the [/account/verfication](https://appwrite.io/docs/references/cloud/client-web/account#createVerification) route to start verifying the user email address. To allow the new user to login to their new account, you need to create a new [account session](https://appwrite.io/docs/references/cloud/client-web/account#createEmailSession).\n     *\n     * @param {string} userId\n     * @param {string} email\n     * @param {string} password\n     * @param {string} name\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.User<Preferences>>}\n     */\n    async create<Preferences extends Models.Preferences>(userId: string, email: string, password: string, name?: string): Promise<Models.User<Preferences>> {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n        if (typeof email === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"email\"');\n        }\n        if (typeof password === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"password\"');\n        }\n        const apiPath = '/account';\n        const payload: Payload = {};\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof email !== 'undefined') {\n            payload['email'] = email;\n        }\n        if (typeof password !== 'undefined') {\n            payload['password'] = password;\n        }\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * Update email\n     *\n     * Update currently logged in user account email address. After changing user address, the user confirmation status will get reset. A new confirmation email is not sent automatically however you can use the send confirmation email endpoint again to send the confirmation email. For security measures, user password is required to complete this request.\nThis endpoint can also be used to convert an anonymous account to a normal one, by passing an email address and a new password.\n\n     *\n     * @param {string} email\n     * @param {string} password\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.User<Preferences>>}\n     */\n    async updateEmail<Preferences extends Models.Preferences>(email: string, password: string): Promise<Models.User<Preferences>> {\n        if (typeof email === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"email\"');\n        }\n        if (typeof password === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"password\"');\n        }\n        const apiPath = '/account/email';\n        const payload: Payload = {};\n        if (typeof email !== 'undefined') {\n            payload['email'] = email;\n        }\n        if (typeof password !== 'undefined') {\n            payload['password'] = password;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'patch',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * List identities\n     *\n     * Get the list of identities for the currently logged in user.\n     *\n     * @param {string[]} queries\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.IdentityList>}\n     */\n    async listIdentities(queries?: string[]): Promise<Models.IdentityList> {\n        const apiPath = '/account/identities';\n        const payload: Payload = {};\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * Delete identity\n     *\n     * Delete an identity by its unique ID.\n     *\n     * @param {string} identityId\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    async deleteIdentity(identityId: string): Promise<{}> {\n        if (typeof identityId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"identityId\"');\n        }\n        const apiPath = '/account/identities/{identityId}'.replace('{identityId}', identityId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'delete',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * Create JWT\n     *\n     * Use this endpoint to create a JSON Web Token. You can use the resulting JWT to authenticate on behalf of the current user when working with the Appwrite server-side API and SDKs. The JWT secret is valid for 15 minutes from its creation and will be invalid if the user will logout in that time frame.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Jwt>}\n     */\n    async createJWT(): Promise<Models.Jwt> {\n        const apiPath = '/account/jwts';\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * List logs\n     *\n     * Get the list of latest security activity logs for the currently logged in user. Each log returns user IP address, location and date and time of log.\n     *\n     * @param {string[]} queries\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.LogList>}\n     */\n    async listLogs(queries?: string[]): Promise<Models.LogList> {\n        const apiPath = '/account/logs';\n        const payload: Payload = {};\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * Update MFA\n     *\n     * Enable or disable MFA on an account.\n     *\n     * @param {boolean} mfa\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.User<Preferences>>}\n     */\n    async updateMFA<Preferences extends Models.Preferences>(mfa: boolean): Promise<Models.User<Preferences>> {\n        if (typeof mfa === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"mfa\"');\n        }\n        const apiPath = '/account/mfa';\n        const payload: Payload = {};\n        if (typeof mfa !== 'undefined') {\n            payload['mfa'] = mfa;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'patch',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * Create authenticator\n     *\n     * Add an authenticator app to be used as an MFA factor. Verify the authenticator using the [verify authenticator](/docs/references/cloud/client-web/account#updateMfaAuthenticator) method.\n     *\n     * @param {AuthenticatorType} type\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.MfaType>}\n     */\n    async createMfaAuthenticator(type: AuthenticatorType): Promise<Models.MfaType> {\n        if (typeof type === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"type\"');\n        }\n        const apiPath = '/account/mfa/authenticators/{type}'.replace('{type}', type);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * Verify authenticator\n     *\n     * Verify an authenticator app after adding it using the [add authenticator](/docs/references/cloud/client-web/account#createMfaAuthenticator) method.\n     *\n     * @param {AuthenticatorType} type\n     * @param {string} otp\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.User<Preferences>>}\n     */\n    async updateMfaAuthenticator<Preferences extends Models.Preferences>(type: AuthenticatorType, otp: string): Promise<Models.User<Preferences>> {\n        if (typeof type === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"type\"');\n        }\n        if (typeof otp === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"otp\"');\n        }\n        const apiPath = '/account/mfa/authenticators/{type}'.replace('{type}', type);\n        const payload: Payload = {};\n        if (typeof otp !== 'undefined') {\n            payload['otp'] = otp;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'put',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * Delete authenticator\n     *\n     * Delete an authenticator for a user by ID.\n     *\n     * @param {AuthenticatorType} type\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    async deleteMfaAuthenticator(type: AuthenticatorType): Promise<{}> {\n        if (typeof type === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"type\"');\n        }\n        const apiPath = '/account/mfa/authenticators/{type}'.replace('{type}', type);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'delete',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * Create MFA challenge\n     *\n     * Begin the process of MFA verification after sign-in. Finish the flow with [updateMfaChallenge](/docs/references/cloud/client-web/account#updateMfaChallenge) method.\n     *\n     * @param {AuthenticationFactor} factor\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.MfaChallenge>}\n     */\n    async createMfaChallenge(factor: AuthenticationFactor): Promise<Models.MfaChallenge> {\n        if (typeof factor === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"factor\"');\n        }\n        const apiPath = '/account/mfa/challenge';\n        const payload: Payload = {};\n        if (typeof factor !== 'undefined') {\n            payload['factor'] = factor;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * Create MFA challenge (confirmation)\n     *\n     * Complete the MFA challenge by providing the one-time password. Finish the process of MFA verification by providing the one-time password. To begin the flow, use [createMfaChallenge](/docs/references/cloud/client-web/account#createMfaChallenge) method.\n     *\n     * @param {string} challengeId\n     * @param {string} otp\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Session>}\n     */\n    async updateMfaChallenge(challengeId: string, otp: string): Promise<Models.Session> {\n        if (typeof challengeId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"challengeId\"');\n        }\n        if (typeof otp === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"otp\"');\n        }\n        const apiPath = '/account/mfa/challenge';\n        const payload: Payload = {};\n        if (typeof challengeId !== 'undefined') {\n            payload['challengeId'] = challengeId;\n        }\n        if (typeof otp !== 'undefined') {\n            payload['otp'] = otp;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'put',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * List factors\n     *\n     * List the factors available on the account to be used as a MFA challange.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.MfaFactors>}\n     */\n    async listMfaFactors(): Promise<Models.MfaFactors> {\n        const apiPath = '/account/mfa/factors';\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * Get MFA recovery codes\n     *\n     * Get recovery codes that can be used as backup for MFA flow. Before getting codes, they must be generated using [createMfaRecoveryCodes](/docs/references/cloud/client-web/account#createMfaRecoveryCodes) method. An OTP challenge is required to read recovery codes.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.MfaRecoveryCodes>}\n     */\n    async getMfaRecoveryCodes(): Promise<Models.MfaRecoveryCodes> {\n        const apiPath = '/account/mfa/recovery-codes';\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * Create MFA recovery codes\n     *\n     * Generate recovery codes as backup for MFA flow. It&#039;s recommended to generate and show then immediately after user successfully adds their authehticator. Recovery codes can be used as a MFA verification type in [createMfaChallenge](/docs/references/cloud/client-web/account#createMfaChallenge) method.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.MfaRecoveryCodes>}\n     */\n    async createMfaRecoveryCodes(): Promise<Models.MfaRecoveryCodes> {\n        const apiPath = '/account/mfa/recovery-codes';\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * Regenerate MFA recovery codes\n     *\n     * Regenerate recovery codes that can be used as backup for MFA flow. Before regenerating codes, they must be first generated using [createMfaRecoveryCodes](/docs/references/cloud/client-web/account#createMfaRecoveryCodes) method. An OTP challenge is required to regenreate recovery codes.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.MfaRecoveryCodes>}\n     */\n    async updateMfaRecoveryCodes(): Promise<Models.MfaRecoveryCodes> {\n        const apiPath = '/account/mfa/recovery-codes';\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'patch',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * Update name\n     *\n     * Update currently logged in user account name.\n     *\n     * @param {string} name\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.User<Preferences>>}\n     */\n    async updateName<Preferences extends Models.Preferences>(name: string): Promise<Models.User<Preferences>> {\n        if (typeof name === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"name\"');\n        }\n        const apiPath = '/account/name';\n        const payload: Payload = {};\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'patch',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * Update password\n     *\n     * Update currently logged in user password. For validation, user is required to pass in the new password, and the old password. For users created with OAuth, Team Invites and Magic URL, oldPassword is optional.\n     *\n     * @param {string} password\n     * @param {string} oldPassword\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.User<Preferences>>}\n     */\n    async updatePassword<Preferences extends Models.Preferences>(password: string, oldPassword?: string): Promise<Models.User<Preferences>> {\n        if (typeof password === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"password\"');\n        }\n        const apiPath = '/account/password';\n        const payload: Payload = {};\n        if (typeof password !== 'undefined') {\n            payload['password'] = password;\n        }\n        if (typeof oldPassword !== 'undefined') {\n            payload['oldPassword'] = oldPassword;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'patch',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * Update phone\n     *\n     * Update the currently logged in user&#039;s phone number. After updating the phone number, the phone verification status will be reset. A confirmation SMS is not sent automatically, however you can use the [POST /account/verification/phone](https://appwrite.io/docs/references/cloud/client-web/account#createPhoneVerification) endpoint to send a confirmation SMS.\n     *\n     * @param {string} phone\n     * @param {string} password\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.User<Preferences>>}\n     */\n    async updatePhone<Preferences extends Models.Preferences>(phone: string, password: string): Promise<Models.User<Preferences>> {\n        if (typeof phone === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"phone\"');\n        }\n        if (typeof password === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"password\"');\n        }\n        const apiPath = '/account/phone';\n        const payload: Payload = {};\n        if (typeof phone !== 'undefined') {\n            payload['phone'] = phone;\n        }\n        if (typeof password !== 'undefined') {\n            payload['password'] = password;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'patch',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * Get account preferences\n     *\n     * Get the preferences as a key-value object for the currently logged in user.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Preferences>}\n     */\n    async getPrefs<Preferences extends Models.Preferences>(): Promise<Preferences> {\n        const apiPath = '/account/prefs';\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * Update preferences\n     *\n     * Update currently logged in user account preferences. The object you pass is stored as is, and replaces any previous value. The maximum allowed prefs size is 64kB and throws error if exceeded.\n     *\n     * @param {Partial<Preferences>} prefs\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.User<Preferences>>}\n     */\n    async updatePrefs<Preferences extends Models.Preferences>(prefs: Partial<Preferences>): Promise<Models.User<Preferences>> {\n        if (typeof prefs === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"prefs\"');\n        }\n        const apiPath = '/account/prefs';\n        const payload: Payload = {};\n        if (typeof prefs !== 'undefined') {\n            payload['prefs'] = prefs;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'patch',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * Create password recovery\n     *\n     * Sends the user an email with a temporary secret key for password reset. When the user clicks the confirmation link he is redirected back to your app password reset URL with the secret key and email address values attached to the URL query string. Use the query string params to submit a request to the [PUT /account/recovery](https://appwrite.io/docs/references/cloud/client-web/account#updateRecovery) endpoint to complete the process. The verification link sent to the user&#039;s email address is valid for 1 hour.\n     *\n     * @param {string} email\n     * @param {string} url\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Token>}\n     */\n    async createRecovery(email: string, url: string): Promise<Models.Token> {\n        if (typeof email === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"email\"');\n        }\n        if (typeof url === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"url\"');\n        }\n        const apiPath = '/account/recovery';\n        const payload: Payload = {};\n        if (typeof email !== 'undefined') {\n            payload['email'] = email;\n        }\n        if (typeof url !== 'undefined') {\n            payload['url'] = url;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * Create password recovery (confirmation)\n     *\n     * Use this endpoint to complete the user account password reset. Both the **userId** and **secret** arguments will be passed as query parameters to the redirect URL you have provided when sending your request to the [POST /account/recovery](https://appwrite.io/docs/references/cloud/client-web/account#createRecovery) endpoint.\n\nPlease note that in order to avoid a [Redirect Attack](https://github.com/OWASP/CheatSheetSeries/blob/master/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.md) the only valid redirect URLs are the ones from domains you have set when adding your platforms in the console interface.\n     *\n     * @param {string} userId\n     * @param {string} secret\n     * @param {string} password\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Token>}\n     */\n    async updateRecovery(userId: string, secret: string, password: string): Promise<Models.Token> {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n        if (typeof secret === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"secret\"');\n        }\n        if (typeof password === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"password\"');\n        }\n        const apiPath = '/account/recovery';\n        const payload: Payload = {};\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof secret !== 'undefined') {\n            payload['secret'] = secret;\n        }\n        if (typeof password !== 'undefined') {\n            payload['password'] = password;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'put',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * List sessions\n     *\n     * Get the list of active sessions across different devices for the currently logged in user.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.SessionList>}\n     */\n    async listSessions(): Promise<Models.SessionList> {\n        const apiPath = '/account/sessions';\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * Delete sessions\n     *\n     * Delete all sessions from the user account and remove any sessions cookies from the end client.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    async deleteSessions(): Promise<{}> {\n        const apiPath = '/account/sessions';\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'delete',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * Create anonymous session\n     *\n     * Use this endpoint to allow a new user to register an anonymous account in your project. This route will also create a new session for the user. To allow the new user to convert an anonymous account to a normal account, you need to update its [email and password](https://appwrite.io/docs/references/cloud/client-web/account#updateEmail) or create an [OAuth2 session](https://appwrite.io/docs/references/cloud/client-web/account#CreateOAuth2Session).\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Session>}\n     */\n    async createAnonymousSession(): Promise<Models.Session> {\n        const apiPath = '/account/sessions/anonymous';\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * Create email password session\n     *\n     * Allow the user to login into their account by providing a valid email and password combination. This route will create a new session for the user.\n\nA user is limited to 10 active sessions at a time by default. [Learn more about session limits](https://appwrite.io/docs/authentication-security#limits).\n     *\n     * @param {string} email\n     * @param {string} password\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Session>}\n     */\n    async createEmailPasswordSession(email: string, password: string): Promise<Models.Session> {\n        if (typeof email === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"email\"');\n        }\n        if (typeof password === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"password\"');\n        }\n        const apiPath = '/account/sessions/email';\n        const payload: Payload = {};\n        if (typeof email !== 'undefined') {\n            payload['email'] = email;\n        }\n        if (typeof password !== 'undefined') {\n            payload['password'] = password;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * Update magic URL session\n     *\n     * Use this endpoint to create a session from token. Provide the **userId** and **secret** parameters from the successful response of authentication flows initiated by token creation. For example, magic URL and phone login.\n     *\n     * @param {string} userId\n     * @param {string} secret\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Session>}\n     */\n    async updateMagicURLSession(userId: string, secret: string): Promise<Models.Session> {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n        if (typeof secret === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"secret\"');\n        }\n        const apiPath = '/account/sessions/magic-url';\n        const payload: Payload = {};\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof secret !== 'undefined') {\n            payload['secret'] = secret;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'put',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * Create OAuth2 session\n     *\n     * Allow the user to login to their account using the OAuth2 provider of their choice. Each OAuth2 provider should be enabled from the Appwrite console first. Use the success and failure arguments to provide a redirect URL&#039;s back to your app when login is completed.\n\nIf there is already an active session, the new session will be attached to the logged-in account. If there are no active sessions, the server will attempt to look for a user with the same email address as the email received from the OAuth2 provider and attach the new session to the existing user. If no matching user is found - the server will create a new user.\n\nA user is limited to 10 active sessions at a time by default. [Learn more about session limits](https://appwrite.io/docs/authentication-security#limits).\n\n     *\n     * @param {OAuthProvider} provider\n     * @param {string} success\n     * @param {string} failure\n     * @param {string[]} scopes\n     * @throws {AppwriteException}\n     * @returns {Promise<void | string>}\n     */\n    async createOAuth2Session(provider: OAuthProvider, success?: string, failure?: string, scopes?: string[]): Promise<void | string> {\n        if (typeof provider === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"provider\"');\n        }\n        const apiPath = '/account/sessions/oauth2/{provider}'.replace('{provider}', provider);\n        const payload: Payload = {};\n        if (typeof success !== 'undefined') {\n            payload['success'] = success;\n        }\n        if (typeof failure !== 'undefined') {\n            payload['failure'] = failure;\n        }\n        if (typeof scopes !== 'undefined') {\n            payload['scopes'] = scopes;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        payload['project'] = this.client.config.project;\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n\n        if (typeof window !== 'undefined' && window?.location) {\n            window.location.href = uri.toString();\n            return;\n        } else {\n            return uri.toString();\n        }\n    }\n    /**\n     * Update phone session\n     *\n     * Use this endpoint to create a session from token. Provide the **userId** and **secret** parameters from the successful response of authentication flows initiated by token creation. For example, magic URL and phone login.\n     *\n     * @param {string} userId\n     * @param {string} secret\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Session>}\n     */\n    async updatePhoneSession(userId: string, secret: string): Promise<Models.Session> {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n        if (typeof secret === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"secret\"');\n        }\n        const apiPath = '/account/sessions/phone';\n        const payload: Payload = {};\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof secret !== 'undefined') {\n            payload['secret'] = secret;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'put',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * Create session\n     *\n     * Use this endpoint to create a session from token. Provide the **userId** and **secret** parameters from the successful response of authentication flows initiated by token creation. For example, magic URL and phone login.\n     *\n     * @param {string} userId\n     * @param {string} secret\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Session>}\n     */\n    async createSession(userId: string, secret: string): Promise<Models.Session> {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n        if (typeof secret === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"secret\"');\n        }\n        const apiPath = '/account/sessions/token';\n        const payload: Payload = {};\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof secret !== 'undefined') {\n            payload['secret'] = secret;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * Get session\n     *\n     * Use this endpoint to get a logged in user&#039;s session using a Session ID. Inputting &#039;current&#039; will return the current session being used.\n     *\n     * @param {string} sessionId\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Session>}\n     */\n    async getSession(sessionId: string): Promise<Models.Session> {\n        if (typeof sessionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"sessionId\"');\n        }\n        const apiPath = '/account/sessions/{sessionId}'.replace('{sessionId}', sessionId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * Update session\n     *\n     * Use this endpoint to extend a session&#039;s length. Extending a session is useful when session expiry is short. If the session was created using an OAuth provider, this endpoint refreshes the access token from the provider.\n     *\n     * @param {string} sessionId\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Session>}\n     */\n    async updateSession(sessionId: string): Promise<Models.Session> {\n        if (typeof sessionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"sessionId\"');\n        }\n        const apiPath = '/account/sessions/{sessionId}'.replace('{sessionId}', sessionId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'patch',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * Delete session\n     *\n     * Logout the user. Use &#039;current&#039; as the session ID to logout on this device, use a session ID to logout on another device. If you&#039;re looking to logout the user on all devices, use [Delete Sessions](https://appwrite.io/docs/references/cloud/client-web/account#deleteSessions) instead.\n     *\n     * @param {string} sessionId\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    async deleteSession(sessionId: string): Promise<{}> {\n        if (typeof sessionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"sessionId\"');\n        }\n        const apiPath = '/account/sessions/{sessionId}'.replace('{sessionId}', sessionId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'delete',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * Update status\n     *\n     * Block the currently logged in user account. Behind the scene, the user record is not deleted but permanently blocked from any access. To completely delete a user, use the Users API instead.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.User<Preferences>>}\n     */\n    async updateStatus<Preferences extends Models.Preferences>(): Promise<Models.User<Preferences>> {\n        const apiPath = '/account/status';\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'patch',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * Create push target\n     *\n     * Use this endpoint to register a device for push notifications. Provide a target ID (custom or generated using ID.unique()), a device identifier (usually a device token), and optionally specify which provider should send notifications to this target. The target is automatically linked to the current session and includes device information like brand and model.\n     *\n     * @param {string} targetId\n     * @param {string} identifier\n     * @param {string} providerId\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Target>}\n     */\n    async createPushTarget(targetId: string, identifier: string, providerId?: string): Promise<Models.Target> {\n        if (typeof targetId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"targetId\"');\n        }\n        if (typeof identifier === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"identifier\"');\n        }\n        const apiPath = '/account/targets/push';\n        const payload: Payload = {};\n        if (typeof targetId !== 'undefined') {\n            payload['targetId'] = targetId;\n        }\n        if (typeof identifier !== 'undefined') {\n            payload['identifier'] = identifier;\n        }\n        if (typeof providerId !== 'undefined') {\n            payload['providerId'] = providerId;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * Update push target\n     *\n     * Update the currently logged in user&#039;s push notification target. You can modify the target&#039;s identifier (device token) and provider ID (token, email, phone etc.). The target must exist and belong to the current user. If you change the provider ID, notifications will be sent through the new messaging provider instead.\n     *\n     * @param {string} targetId\n     * @param {string} identifier\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Target>}\n     */\n    async updatePushTarget(targetId: string, identifier: string): Promise<Models.Target> {\n        if (typeof targetId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"targetId\"');\n        }\n        if (typeof identifier === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"identifier\"');\n        }\n        const apiPath = '/account/targets/{targetId}/push'.replace('{targetId}', targetId);\n        const payload: Payload = {};\n        if (typeof identifier !== 'undefined') {\n            payload['identifier'] = identifier;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'put',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * Delete push target\n     *\n     * Delete a push notification target for the currently logged in user. After deletion, the device will no longer receive push notifications. The target must exist and belong to the current user.\n     *\n     * @param {string} targetId\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    async deletePushTarget(targetId: string): Promise<{}> {\n        if (typeof targetId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"targetId\"');\n        }\n        const apiPath = '/account/targets/{targetId}/push'.replace('{targetId}', targetId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'delete',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * Create email token (OTP)\n     *\n     * Sends the user an email with a secret key for creating a session. If the provided user ID has not be registered, a new user will be created. Use the returned user ID and secret and submit a request to the [POST /v1/account/sessions/token](https://appwrite.io/docs/references/cloud/client-web/account#createSession) endpoint to complete the login process. The secret sent to the user&#039;s email is valid for 15 minutes.\n\nA user is limited to 10 active sessions at a time by default. [Learn more about session limits](https://appwrite.io/docs/authentication-security#limits).\n     *\n     * @param {string} userId\n     * @param {string} email\n     * @param {boolean} phrase\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Token>}\n     */\n    async createEmailToken(userId: string, email: string, phrase?: boolean): Promise<Models.Token> {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n        if (typeof email === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"email\"');\n        }\n        const apiPath = '/account/tokens/email';\n        const payload: Payload = {};\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof email !== 'undefined') {\n            payload['email'] = email;\n        }\n        if (typeof phrase !== 'undefined') {\n            payload['phrase'] = phrase;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * Create magic URL token\n     *\n     * Sends the user an email with a secret key for creating a session. If the provided user ID has not been registered, a new user will be created. When the user clicks the link in the email, the user is redirected back to the URL you provided with the secret key and userId values attached to the URL query string. Use the query string parameters to submit a request to the [POST /v1/account/sessions/token](https://appwrite.io/docs/references/cloud/client-web/account#createSession) endpoint to complete the login process. The link sent to the user&#039;s email address is valid for 1 hour.\n\nA user is limited to 10 active sessions at a time by default. [Learn more about session limits](https://appwrite.io/docs/authentication-security#limits).\n\n     *\n     * @param {string} userId\n     * @param {string} email\n     * @param {string} url\n     * @param {boolean} phrase\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Token>}\n     */\n    async createMagicURLToken(userId: string, email: string, url?: string, phrase?: boolean): Promise<Models.Token> {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n        if (typeof email === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"email\"');\n        }\n        const apiPath = '/account/tokens/magic-url';\n        const payload: Payload = {};\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof email !== 'undefined') {\n            payload['email'] = email;\n        }\n        if (typeof url !== 'undefined') {\n            payload['url'] = url;\n        }\n        if (typeof phrase !== 'undefined') {\n            payload['phrase'] = phrase;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * Create OAuth2 token\n     *\n     * Allow the user to login to their account using the OAuth2 provider of their choice. Each OAuth2 provider should be enabled from the Appwrite console first. Use the success and failure arguments to provide a redirect URL&#039;s back to your app when login is completed. \n\nIf authentication succeeds, `userId` and `secret` of a token will be appended to the success URL as query parameters. These can be used to create a new session using the [Create session](https://appwrite.io/docs/references/cloud/client-web/account#createSession) endpoint.\n\nA user is limited to 10 active sessions at a time by default. [Learn more about session limits](https://appwrite.io/docs/authentication-security#limits).\n     *\n     * @param {OAuthProvider} provider\n     * @param {string} success\n     * @param {string} failure\n     * @param {string[]} scopes\n     * @throws {AppwriteException}\n     * @returns {Promise<void | string>}\n     */\n    async createOAuth2Token(provider: OAuthProvider, success?: string, failure?: string, scopes?: string[]): Promise<void | string> {\n        if (typeof provider === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"provider\"');\n        }\n        const apiPath = '/account/tokens/oauth2/{provider}'.replace('{provider}', provider);\n        const payload: Payload = {};\n        if (typeof success !== 'undefined') {\n            payload['success'] = success;\n        }\n        if (typeof failure !== 'undefined') {\n            payload['failure'] = failure;\n        }\n        if (typeof scopes !== 'undefined') {\n            payload['scopes'] = scopes;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        payload['project'] = this.client.config.project;\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n\n        if (typeof window !== 'undefined' && window?.location) {\n            window.location.href = uri.toString();\n            return;\n        } else {\n            return uri.toString();\n        }\n    }\n    /**\n     * Create phone token\n     *\n     * Sends the user an SMS with a secret key for creating a session. If the provided user ID has not be registered, a new user will be created. Use the returned user ID and secret and submit a request to the [POST /v1/account/sessions/token](https://appwrite.io/docs/references/cloud/client-web/account#createSession) endpoint to complete the login process. The secret sent to the user&#039;s phone is valid for 15 minutes.\n\nA user is limited to 10 active sessions at a time by default. [Learn more about session limits](https://appwrite.io/docs/authentication-security#limits).\n     *\n     * @param {string} userId\n     * @param {string} phone\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Token>}\n     */\n    async createPhoneToken(userId: string, phone: string): Promise<Models.Token> {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n        if (typeof phone === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"phone\"');\n        }\n        const apiPath = '/account/tokens/phone';\n        const payload: Payload = {};\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof phone !== 'undefined') {\n            payload['phone'] = phone;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * Create email verification\n     *\n     * Use this endpoint to send a verification message to your user email address to confirm they are the valid owners of that address. Both the **userId** and **secret** arguments will be passed as query parameters to the URL you have provided to be attached to the verification email. The provided URL should redirect the user back to your app and allow you to complete the verification process by verifying both the **userId** and **secret** parameters. Learn more about how to [complete the verification process](https://appwrite.io/docs/references/cloud/client-web/account#updateVerification). The verification link sent to the user&#039;s email address is valid for 7 days.\n\nPlease note that in order to avoid a [Redirect Attack](https://github.com/OWASP/CheatSheetSeries/blob/master/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.md), the only valid redirect URLs are the ones from domains you have set when adding your platforms in the console interface.\n\n     *\n     * @param {string} url\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Token>}\n     */\n    async createVerification(url: string): Promise<Models.Token> {\n        if (typeof url === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"url\"');\n        }\n        const apiPath = '/account/verification';\n        const payload: Payload = {};\n        if (typeof url !== 'undefined') {\n            payload['url'] = url;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * Create email verification (confirmation)\n     *\n     * Use this endpoint to complete the user email verification process. Use both the **userId** and **secret** parameters that were attached to your app URL to verify the user email ownership. If confirmed this route will return a 200 status code.\n     *\n     * @param {string} userId\n     * @param {string} secret\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Token>}\n     */\n    async updateVerification(userId: string, secret: string): Promise<Models.Token> {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n        if (typeof secret === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"secret\"');\n        }\n        const apiPath = '/account/verification';\n        const payload: Payload = {};\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof secret !== 'undefined') {\n            payload['secret'] = secret;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'put',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * Create phone verification\n     *\n     * Use this endpoint to send a verification SMS to the currently logged in user. This endpoint is meant for use after updating a user&#039;s phone number using the [accountUpdatePhone](https://appwrite.io/docs/references/cloud/client-web/account#updatePhone) endpoint. Learn more about how to [complete the verification process](https://appwrite.io/docs/references/cloud/client-web/account#updatePhoneVerification). The verification code sent to the user&#039;s phone number is valid for 15 minutes.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Token>}\n     */\n    async createPhoneVerification(): Promise<Models.Token> {\n        const apiPath = '/account/verification/phone';\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * Update phone verification (confirmation)\n     *\n     * Use this endpoint to complete the user phone verification process. Use the **userId** and **secret** that were sent to your user&#039;s phone number to verify the user email ownership. If confirmed this route will return a 200 status code.\n     *\n     * @param {string} userId\n     * @param {string} secret\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Token>}\n     */\n    async updatePhoneVerification(userId: string, secret: string): Promise<Models.Token> {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n        if (typeof secret === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"secret\"');\n        }\n        const apiPath = '/account/verification/phone';\n        const payload: Payload = {};\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof secret !== 'undefined') {\n            payload['secret'] = secret;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'put',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n}\n", "import { Service } from '../service';\nimport { AppwriteException, Client, type Payload, UploadProgress } from '../client';\nimport type { Models } from '../models';\nimport { Browser } from '../enums/browser';\nimport { CreditCard } from '../enums/credit-card';\nimport { Flag } from '../enums/flag';\n\nexport class Avatars {\n    client: Client;\n\n    constructor(client: Client) {\n        this.client = client;\n    }\n\n    /**\n     * Get browser icon\n     *\n     * You can use this endpoint to show different browser icons to your users. The code argument receives the browser code as it appears in your user [GET /account/sessions](https://appwrite.io/docs/references/cloud/client-web/account#getSessions) endpoint. Use width, height and quality arguments to change the output settings.\n\nWhen one dimension is specified and the other is 0, the image is scaled with preserved aspect ratio. If both dimensions are 0, the API provides an image at source quality. If dimensions are not specified, the default size of image returned is 100x100px.\n     *\n     * @param {Browser} code\n     * @param {number} width\n     * @param {number} height\n     * @param {number} quality\n     * @throws {AppwriteException}\n     * @returns {string}\n     */\n    getBrowser(code: Browser, width?: number, height?: number, quality?: number): string {\n        if (typeof code === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"code\"');\n        }\n        const apiPath = '/avatars/browsers/{code}'.replace('{code}', code);\n        const payload: Payload = {};\n        if (typeof width !== 'undefined') {\n            payload['width'] = width;\n        }\n        if (typeof height !== 'undefined') {\n            payload['height'] = height;\n        }\n        if (typeof quality !== 'undefined') {\n            payload['quality'] = quality;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        payload['project'] = this.client.config.project;\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n\n        payload['project'] = this.client.config.project;\n\n        return uri.toString();\n    }\n    /**\n     * Get credit card icon\n     *\n     * The credit card endpoint will return you the icon of the credit card provider you need. Use width, height and quality arguments to change the output settings.\n\nWhen one dimension is specified and the other is 0, the image is scaled with preserved aspect ratio. If both dimensions are 0, the API provides an image at source quality. If dimensions are not specified, the default size of image returned is 100x100px.\n\n     *\n     * @param {CreditCard} code\n     * @param {number} width\n     * @param {number} height\n     * @param {number} quality\n     * @throws {AppwriteException}\n     * @returns {string}\n     */\n    getCreditCard(code: CreditCard, width?: number, height?: number, quality?: number): string {\n        if (typeof code === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"code\"');\n        }\n        const apiPath = '/avatars/credit-cards/{code}'.replace('{code}', code);\n        const payload: Payload = {};\n        if (typeof width !== 'undefined') {\n            payload['width'] = width;\n        }\n        if (typeof height !== 'undefined') {\n            payload['height'] = height;\n        }\n        if (typeof quality !== 'undefined') {\n            payload['quality'] = quality;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        payload['project'] = this.client.config.project;\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n\n        payload['project'] = this.client.config.project;\n\n        return uri.toString();\n    }\n    /**\n     * Get favicon\n     *\n     * Use this endpoint to fetch the favorite icon (AKA favicon) of any remote website URL.\n\nThis endpoint does not follow HTTP redirects.\n     *\n     * @param {string} url\n     * @throws {AppwriteException}\n     * @returns {string}\n     */\n    getFavicon(url: string): string {\n        if (typeof url === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"url\"');\n        }\n        const apiPath = '/avatars/favicon';\n        const payload: Payload = {};\n        if (typeof url !== 'undefined') {\n            payload['url'] = url;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        payload['project'] = this.client.config.project;\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n\n        payload['project'] = this.client.config.project;\n\n        return uri.toString();\n    }\n    /**\n     * Get country flag\n     *\n     * You can use this endpoint to show different country flags icons to your users. The code argument receives the 2 letter country code. Use width, height and quality arguments to change the output settings. Country codes follow the [ISO 3166-1](https://en.wikipedia.org/wiki/ISO_3166-1) standard.\n\nWhen one dimension is specified and the other is 0, the image is scaled with preserved aspect ratio. If both dimensions are 0, the API provides an image at source quality. If dimensions are not specified, the default size of image returned is 100x100px.\n\n     *\n     * @param {Flag} code\n     * @param {number} width\n     * @param {number} height\n     * @param {number} quality\n     * @throws {AppwriteException}\n     * @returns {string}\n     */\n    getFlag(code: Flag, width?: number, height?: number, quality?: number): string {\n        if (typeof code === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"code\"');\n        }\n        const apiPath = '/avatars/flags/{code}'.replace('{code}', code);\n        const payload: Payload = {};\n        if (typeof width !== 'undefined') {\n            payload['width'] = width;\n        }\n        if (typeof height !== 'undefined') {\n            payload['height'] = height;\n        }\n        if (typeof quality !== 'undefined') {\n            payload['quality'] = quality;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        payload['project'] = this.client.config.project;\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n\n        payload['project'] = this.client.config.project;\n\n        return uri.toString();\n    }\n    /**\n     * Get image from URL\n     *\n     * Use this endpoint to fetch a remote image URL and crop it to any image size you want. This endpoint is very useful if you need to crop and display remote images in your app or in case you want to make sure a 3rd party image is properly served using a TLS protocol.\n\nWhen one dimension is specified and the other is 0, the image is scaled with preserved aspect ratio. If both dimensions are 0, the API provides an image at source quality. If dimensions are not specified, the default size of image returned is 400x400px.\n\nThis endpoint does not follow HTTP redirects.\n     *\n     * @param {string} url\n     * @param {number} width\n     * @param {number} height\n     * @throws {AppwriteException}\n     * @returns {string}\n     */\n    getImage(url: string, width?: number, height?: number): string {\n        if (typeof url === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"url\"');\n        }\n        const apiPath = '/avatars/image';\n        const payload: Payload = {};\n        if (typeof url !== 'undefined') {\n            payload['url'] = url;\n        }\n        if (typeof width !== 'undefined') {\n            payload['width'] = width;\n        }\n        if (typeof height !== 'undefined') {\n            payload['height'] = height;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        payload['project'] = this.client.config.project;\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n\n        payload['project'] = this.client.config.project;\n\n        return uri.toString();\n    }\n    /**\n     * Get user initials\n     *\n     * Use this endpoint to show your user initials avatar icon on your website or app. By default, this route will try to print your logged-in user name or email initials. You can also overwrite the user name if you pass the &#039;name&#039; parameter. If no name is given and no user is logged, an empty avatar will be returned.\n\nYou can use the color and background params to change the avatar colors. By default, a random theme will be selected. The random theme will persist for the user&#039;s initials when reloading the same theme will always return for the same initials.\n\nWhen one dimension is specified and the other is 0, the image is scaled with preserved aspect ratio. If both dimensions are 0, the API provides an image at source quality. If dimensions are not specified, the default size of image returned is 100x100px.\n\n     *\n     * @param {string} name\n     * @param {number} width\n     * @param {number} height\n     * @param {string} background\n     * @throws {AppwriteException}\n     * @returns {string}\n     */\n    getInitials(name?: string, width?: number, height?: number, background?: string): string {\n        const apiPath = '/avatars/initials';\n        const payload: Payload = {};\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        if (typeof width !== 'undefined') {\n            payload['width'] = width;\n        }\n        if (typeof height !== 'undefined') {\n            payload['height'] = height;\n        }\n        if (typeof background !== 'undefined') {\n            payload['background'] = background;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        payload['project'] = this.client.config.project;\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n\n        payload['project'] = this.client.config.project;\n\n        return uri.toString();\n    }\n    /**\n     * Get QR code\n     *\n     * Converts a given plain text to a QR code image. You can use the query parameters to change the size and style of the resulting image.\n\n     *\n     * @param {string} text\n     * @param {number} size\n     * @param {number} margin\n     * @param {boolean} download\n     * @throws {AppwriteException}\n     * @returns {string}\n     */\n    getQR(text: string, size?: number, margin?: number, download?: boolean): string {\n        if (typeof text === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"text\"');\n        }\n        const apiPath = '/avatars/qr';\n        const payload: Payload = {};\n        if (typeof text !== 'undefined') {\n            payload['text'] = text;\n        }\n        if (typeof size !== 'undefined') {\n            payload['size'] = size;\n        }\n        if (typeof margin !== 'undefined') {\n            payload['margin'] = margin;\n        }\n        if (typeof download !== 'undefined') {\n            payload['download'] = download;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        payload['project'] = this.client.config.project;\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n\n        payload['project'] = this.client.config.project;\n\n        return uri.toString();\n    }\n}\n", "import { Service } from '../service';\nimport { AppwriteException, Client, type Payload, UploadProgress } from '../client';\nimport type { Models } from '../models';\n\nexport class Databases {\n    client: Client;\n\n    constructor(client: Client) {\n        this.client = client;\n    }\n\n    /**\n     * List documents\n     *\n     * Get a list of all the user&#039;s documents in a given collection. You can use the query params to filter your results.\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string[]} queries\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.DocumentList<Document>>}\n     */\n    async listDocuments<Document extends Models.Document>(databaseId: string, collectionId: string, queries?: string[]): Promise<Models.DocumentList<Document>> {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/documents'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId);\n        const payload: Payload = {};\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * Create document\n     *\n     * Create a new Document. Before using this route, you should create a new collection resource using either a [server integration](https://appwrite.io/docs/server/databases#databasesCreateCollection) API or directly from your database console.\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string} documentId\n     * @param {Omit<Document, keyof Models.Document>} data\n     * @param {string[]} permissions\n     * @throws {AppwriteException}\n     * @returns {Promise<Document>}\n     */\n    async createDocument<Document extends Models.Document>(databaseId: string, collectionId: string, documentId: string, data: Omit<Document, keyof Models.Document>, permissions?: string[]): Promise<Document> {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        if (typeof documentId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"documentId\"');\n        }\n        if (typeof data === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"data\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/documents'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId);\n        const payload: Payload = {};\n        if (typeof documentId !== 'undefined') {\n            payload['documentId'] = documentId;\n        }\n        if (typeof data !== 'undefined') {\n            payload['data'] = data;\n        }\n        if (typeof permissions !== 'undefined') {\n            payload['permissions'] = permissions;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * Get document\n     *\n     * Get a document by its unique ID. This endpoint response returns a JSON object with the document data.\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string} documentId\n     * @param {string[]} queries\n     * @throws {AppwriteException}\n     * @returns {Promise<Document>}\n     */\n    async getDocument<Document extends Models.Document>(databaseId: string, collectionId: string, documentId: string, queries?: string[]): Promise<Document> {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        if (typeof documentId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"documentId\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/documents/{documentId}'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId).replace('{documentId}', documentId);\n        const payload: Payload = {};\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * Update document\n     *\n     * Update a document by its unique ID. Using the patch method you can pass only specific fields that will get updated.\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string} documentId\n     * @param {Partial<Omit<Document, keyof Models.Document>>} data\n     * @param {string[]} permissions\n     * @throws {AppwriteException}\n     * @returns {Promise<Document>}\n     */\n    async updateDocument<Document extends Models.Document>(databaseId: string, collectionId: string, documentId: string, data?: Partial<Omit<Document, keyof Models.Document>>, permissions?: string[]): Promise<Document> {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        if (typeof documentId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"documentId\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/documents/{documentId}'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId).replace('{documentId}', documentId);\n        const payload: Payload = {};\n        if (typeof data !== 'undefined') {\n            payload['data'] = data;\n        }\n        if (typeof permissions !== 'undefined') {\n            payload['permissions'] = permissions;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'patch',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * Delete document\n     *\n     * Delete a document by its unique ID.\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string} documentId\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    async deleteDocument(databaseId: string, collectionId: string, documentId: string): Promise<{}> {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        if (typeof documentId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"documentId\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/documents/{documentId}'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId).replace('{documentId}', documentId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'delete',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n}\n", "import { Service } from '../service';\nimport { AppwriteException, Client, type Payload, UploadProgress } from '../client';\nimport type { Models } from '../models';\nimport { ExecutionMethod } from '../enums/execution-method';\n\nexport class Functions {\n    client: Client;\n\n    constructor(client: Client) {\n        this.client = client;\n    }\n\n    /**\n     * List executions\n     *\n     * Get a list of all the current user function execution logs. You can use the query params to filter your results.\n     *\n     * @param {string} functionId\n     * @param {string[]} queries\n     * @param {string} search\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.ExecutionList>}\n     */\n    async listExecutions(functionId: string, queries?: string[], search?: string): Promise<Models.ExecutionList> {\n        if (typeof functionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"functionId\"');\n        }\n        const apiPath = '/functions/{functionId}/executions'.replace('{functionId}', functionId);\n        const payload: Payload = {};\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n        if (typeof search !== 'undefined') {\n            payload['search'] = search;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * Create execution\n     *\n     * Trigger a function execution. The returned object will return you the current execution status. You can ping the `Get Execution` endpoint to get updates on the current execution status. Once this endpoint is called, your function execution process will start asynchronously.\n     *\n     * @param {string} functionId\n     * @param {string} body\n     * @param {boolean} async\n     * @param {string} xpath\n     * @param {ExecutionMethod} method\n     * @param {object} headers\n     * @param {string} scheduledAt\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Execution>}\n     */\n    async createExecution(functionId: string, body?: string, async?: boolean, xpath?: string, method?: ExecutionMethod, headers?: object, scheduledAt?: string): Promise<Models.Execution> {\n        if (typeof functionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"functionId\"');\n        }\n        const apiPath = '/functions/{functionId}/executions'.replace('{functionId}', functionId);\n        const payload: Payload = {};\n        if (typeof body !== 'undefined') {\n            payload['body'] = body;\n        }\n        if (typeof async !== 'undefined') {\n            payload['async'] = async;\n        }\n        if (typeof xpath !== 'undefined') {\n            payload['path'] = xpath;\n        }\n        if (typeof method !== 'undefined') {\n            payload['method'] = method;\n        }\n        if (typeof headers !== 'undefined') {\n            payload['headers'] = headers;\n        }\n        if (typeof scheduledAt !== 'undefined') {\n            payload['scheduledAt'] = scheduledAt;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * Get execution\n     *\n     * Get a function execution log by its unique ID.\n     *\n     * @param {string} functionId\n     * @param {string} executionId\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Execution>}\n     */\n    async getExecution(functionId: string, executionId: string): Promise<Models.Execution> {\n        if (typeof functionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"functionId\"');\n        }\n        if (typeof executionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"executionId\"');\n        }\n        const apiPath = '/functions/{functionId}/executions/{executionId}'.replace('{functionId}', functionId).replace('{executionId}', executionId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n}\n", "import { Service } from '../service';\nimport { AppwriteException, Client, type Payload, UploadProgress } from '../client';\nimport type { Models } from '../models';\n\nexport class Graphql {\n    client: Client;\n\n    constructor(client: Client) {\n        this.client = client;\n    }\n\n    /**\n     * GraphQL endpoint\n     *\n     * Execute a GraphQL mutation.\n     *\n     * @param {object} query\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    async query(query: object): Promise<{}> {\n        if (typeof query === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"query\"');\n        }\n        const apiPath = '/graphql';\n        const payload: Payload = {};\n        if (typeof query !== 'undefined') {\n            payload['query'] = query;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'x-sdk-graphql': 'true',\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * GraphQL endpoint\n     *\n     * Execute a GraphQL mutation.\n     *\n     * @param {object} query\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    async mutation(query: object): Promise<{}> {\n        if (typeof query === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"query\"');\n        }\n        const apiPath = '/graphql/mutation';\n        const payload: Payload = {};\n        if (typeof query !== 'undefined') {\n            payload['query'] = query;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'x-sdk-graphql': 'true',\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n}\n", "import { Service } from '../service';\nimport { AppwriteException, Client, type Payload, UploadProgress } from '../client';\nimport type { Models } from '../models';\n\nexport class Locale {\n    client: Client;\n\n    constructor(client: Client) {\n        this.client = client;\n    }\n\n    /**\n     * Get user locale\n     *\n     * Get the current user location based on IP. Returns an object with user country code, country name, continent name, continent code, ip address and suggested currency. You can use the locale header to get the data in a supported language.\n\n([IP Geolocation by DB-IP](https://db-ip.com))\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Locale>}\n     */\n    async get(): Promise<Models.Locale> {\n        const apiPath = '/locale';\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * List locale codes\n     *\n     * List of all locale codes in [ISO 639-1](https://en.wikipedia.org/wiki/List_of_ISO_639-1_codes).\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.LocaleCodeList>}\n     */\n    async listCodes(): Promise<Models.LocaleCodeList> {\n        const apiPath = '/locale/codes';\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * List continents\n     *\n     * List of all continents. You can use the locale header to get the data in a supported language.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.ContinentList>}\n     */\n    async listContinents(): Promise<Models.ContinentList> {\n        const apiPath = '/locale/continents';\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * List countries\n     *\n     * List of all countries. You can use the locale header to get the data in a supported language.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.CountryList>}\n     */\n    async listCountries(): Promise<Models.CountryList> {\n        const apiPath = '/locale/countries';\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * List EU countries\n     *\n     * List of all countries that are currently members of the EU. You can use the locale header to get the data in a supported language.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.CountryList>}\n     */\n    async listCountriesEU(): Promise<Models.CountryList> {\n        const apiPath = '/locale/countries/eu';\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * List countries phone codes\n     *\n     * List of all countries phone codes. You can use the locale header to get the data in a supported language.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.PhoneList>}\n     */\n    async listCountriesPhones(): Promise<Models.PhoneList> {\n        const apiPath = '/locale/countries/phones';\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * List currencies\n     *\n     * List of all currencies, including currency symbol, name, plural, and decimal digits for all major and minor currencies. You can use the locale header to get the data in a supported language.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.CurrencyList>}\n     */\n    async listCurrencies(): Promise<Models.CurrencyList> {\n        const apiPath = '/locale/currencies';\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * List languages\n     *\n     * List of all languages classified by ISO 639-1 including 2-letter code, name in English, and name in the respective language.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.LanguageList>}\n     */\n    async listLanguages(): Promise<Models.LanguageList> {\n        const apiPath = '/locale/languages';\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n}\n", "import { Service } from '../service';\nimport { AppwriteException, Client, type Payload, UploadProgress } from '../client';\nimport type { Models } from '../models';\n\nexport class Messaging {\n    client: Client;\n\n    constructor(client: Client) {\n        this.client = client;\n    }\n\n    /**\n     * Create subscriber\n     *\n     * Create a new subscriber.\n     *\n     * @param {string} topicId\n     * @param {string} subscriberId\n     * @param {string} targetId\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Subscriber>}\n     */\n    async createSubscriber(topicId: string, subscriberId: string, targetId: string): Promise<Models.Subscriber> {\n        if (typeof topicId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"topicId\"');\n        }\n        if (typeof subscriberId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"subscriberId\"');\n        }\n        if (typeof targetId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"targetId\"');\n        }\n        const apiPath = '/messaging/topics/{topicId}/subscribers'.replace('{topicId}', topicId);\n        const payload: Payload = {};\n        if (typeof subscriberId !== 'undefined') {\n            payload['subscriberId'] = subscriberId;\n        }\n        if (typeof targetId !== 'undefined') {\n            payload['targetId'] = targetId;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * Delete subscriber\n     *\n     * Delete a subscriber by its unique ID.\n     *\n     * @param {string} topicId\n     * @param {string} subscriberId\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    async deleteSubscriber(topicId: string, subscriberId: string): Promise<{}> {\n        if (typeof topicId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"topicId\"');\n        }\n        if (typeof subscriberId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"subscriberId\"');\n        }\n        const apiPath = '/messaging/topics/{topicId}/subscribers/{subscriberId}'.replace('{topicId}', topicId).replace('{subscriberId}', subscriberId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'delete',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n}\n", "import { Service } from '../service';\nimport { AppwriteException, Client, type Payload, UploadProgress } from '../client';\nimport type { Models } from '../models';\nimport { ImageGravity } from '../enums/image-gravity';\nimport { ImageFormat } from '../enums/image-format';\n\nexport class Storage {\n    client: Client;\n\n    constructor(client: Client) {\n        this.client = client;\n    }\n\n    /**\n     * List files\n     *\n     * Get a list of all the user files. You can use the query params to filter your results.\n     *\n     * @param {string} bucketId\n     * @param {string[]} queries\n     * @param {string} search\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.FileList>}\n     */\n    async listFiles(bucketId: string, queries?: string[], search?: string): Promise<Models.FileList> {\n        if (typeof bucketId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"bucketId\"');\n        }\n        const apiPath = '/storage/buckets/{bucketId}/files'.replace('{bucketId}', bucketId);\n        const payload: Payload = {};\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n        if (typeof search !== 'undefined') {\n            payload['search'] = search;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * Create file\n     *\n     * Create a new file. Before using this route, you should create a new bucket resource using either a [server integration](https://appwrite.io/docs/server/storage#storageCreateBucket) API or directly from your Appwrite console.\n\nLarger files should be uploaded using multiple requests with the [content-range](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Content-Range) header to send a partial request with a maximum supported chunk of `5MB`. The `content-range` header values should always be in bytes.\n\nWhen the first request is sent, the server will return the **File** object, and the subsequent part request must include the file&#039;s **id** in `x-appwrite-id` header to allow the server to know that the partial upload is for the existing file and not for a new one.\n\nIf you&#039;re creating a new file using one of the Appwrite SDKs, all the chunking logic will be managed by the SDK internally.\n\n     *\n     * @param {string} bucketId\n     * @param {string} fileId\n     * @param {File} file\n     * @param {string[]} permissions\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.File>}\n     */\n    async createFile(bucketId: string, fileId: string, file: File, permissions?: string[], onProgress = (progress: UploadProgress) => {}): Promise<Models.File> {\n        if (typeof bucketId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"bucketId\"');\n        }\n        if (typeof fileId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"fileId\"');\n        }\n        if (typeof file === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"file\"');\n        }\n        const apiPath = '/storage/buckets/{bucketId}/files'.replace('{bucketId}', bucketId);\n        const payload: Payload = {};\n        if (typeof fileId !== 'undefined') {\n            payload['fileId'] = fileId;\n        }\n        if (typeof file !== 'undefined') {\n            payload['file'] = file;\n        }\n        if (typeof permissions !== 'undefined') {\n            payload['permissions'] = permissions;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'multipart/form-data',\n        }\n\n\n        return await this.client.chunkedUpload(\n            'post',\n            uri,\n            apiHeaders,\n            payload,\n            onProgress\n        );\n    }\n    /**\n     * Get file\n     *\n     * Get a file by its unique ID. This endpoint response returns a JSON object with the file metadata.\n     *\n     * @param {string} bucketId\n     * @param {string} fileId\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.File>}\n     */\n    async getFile(bucketId: string, fileId: string): Promise<Models.File> {\n        if (typeof bucketId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"bucketId\"');\n        }\n        if (typeof fileId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"fileId\"');\n        }\n        const apiPath = '/storage/buckets/{bucketId}/files/{fileId}'.replace('{bucketId}', bucketId).replace('{fileId}', fileId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * Update file\n     *\n     * Update a file by its unique ID. Only users with write permissions have access to update this resource.\n     *\n     * @param {string} bucketId\n     * @param {string} fileId\n     * @param {string} name\n     * @param {string[]} permissions\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.File>}\n     */\n    async updateFile(bucketId: string, fileId: string, name?: string, permissions?: string[]): Promise<Models.File> {\n        if (typeof bucketId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"bucketId\"');\n        }\n        if (typeof fileId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"fileId\"');\n        }\n        const apiPath = '/storage/buckets/{bucketId}/files/{fileId}'.replace('{bucketId}', bucketId).replace('{fileId}', fileId);\n        const payload: Payload = {};\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        if (typeof permissions !== 'undefined') {\n            payload['permissions'] = permissions;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'put',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * Delete file\n     *\n     * Delete a file by its unique ID. Only users with write permissions have access to delete this resource.\n     *\n     * @param {string} bucketId\n     * @param {string} fileId\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    async deleteFile(bucketId: string, fileId: string): Promise<{}> {\n        if (typeof bucketId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"bucketId\"');\n        }\n        if (typeof fileId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"fileId\"');\n        }\n        const apiPath = '/storage/buckets/{bucketId}/files/{fileId}'.replace('{bucketId}', bucketId).replace('{fileId}', fileId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'delete',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * Get file for download\n     *\n     * Get a file content by its unique ID. The endpoint response return with a &#039;Content-Disposition: attachment&#039; header that tells the browser to start downloading the file to user downloads directory.\n     *\n     * @param {string} bucketId\n     * @param {string} fileId\n     * @throws {AppwriteException}\n     * @returns {string}\n     */\n    getFileDownload(bucketId: string, fileId: string): string {\n        if (typeof bucketId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"bucketId\"');\n        }\n        if (typeof fileId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"fileId\"');\n        }\n        const apiPath = '/storage/buckets/{bucketId}/files/{fileId}/download'.replace('{bucketId}', bucketId).replace('{fileId}', fileId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        payload['project'] = this.client.config.project;\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n\n        payload['project'] = this.client.config.project;\n\n        return uri.toString();\n    }\n    /**\n     * Get file preview\n     *\n     * Get a file preview image. Currently, this method supports preview for image files (jpg, png, and gif), other supported formats, like pdf, docs, slides, and spreadsheets, will return the file icon image. You can also pass query string arguments for cutting and resizing your preview image. Preview is supported only for image files smaller than 10MB.\n     *\n     * @param {string} bucketId\n     * @param {string} fileId\n     * @param {number} width\n     * @param {number} height\n     * @param {ImageGravity} gravity\n     * @param {number} quality\n     * @param {number} borderWidth\n     * @param {string} borderColor\n     * @param {number} borderRadius\n     * @param {number} opacity\n     * @param {number} rotation\n     * @param {string} background\n     * @param {ImageFormat} output\n     * @throws {AppwriteException}\n     * @returns {string}\n     */\n    getFilePreview(bucketId: string, fileId: string, width?: number, height?: number, gravity?: ImageGravity, quality?: number, borderWidth?: number, borderColor?: string, borderRadius?: number, opacity?: number, rotation?: number, background?: string, output?: ImageFormat): string {\n        if (typeof bucketId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"bucketId\"');\n        }\n        if (typeof fileId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"fileId\"');\n        }\n        const apiPath = '/storage/buckets/{bucketId}/files/{fileId}/preview'.replace('{bucketId}', bucketId).replace('{fileId}', fileId);\n        const payload: Payload = {};\n        if (typeof width !== 'undefined') {\n            payload['width'] = width;\n        }\n        if (typeof height !== 'undefined') {\n            payload['height'] = height;\n        }\n        if (typeof gravity !== 'undefined') {\n            payload['gravity'] = gravity;\n        }\n        if (typeof quality !== 'undefined') {\n            payload['quality'] = quality;\n        }\n        if (typeof borderWidth !== 'undefined') {\n            payload['borderWidth'] = borderWidth;\n        }\n        if (typeof borderColor !== 'undefined') {\n            payload['borderColor'] = borderColor;\n        }\n        if (typeof borderRadius !== 'undefined') {\n            payload['borderRadius'] = borderRadius;\n        }\n        if (typeof opacity !== 'undefined') {\n            payload['opacity'] = opacity;\n        }\n        if (typeof rotation !== 'undefined') {\n            payload['rotation'] = rotation;\n        }\n        if (typeof background !== 'undefined') {\n            payload['background'] = background;\n        }\n        if (typeof output !== 'undefined') {\n            payload['output'] = output;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        payload['project'] = this.client.config.project;\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n\n        payload['project'] = this.client.config.project;\n\n        return uri.toString();\n    }\n    /**\n     * Get file for view\n     *\n     * Get a file content by its unique ID. This endpoint is similar to the download method but returns with no  &#039;Content-Disposition: attachment&#039; header.\n     *\n     * @param {string} bucketId\n     * @param {string} fileId\n     * @throws {AppwriteException}\n     * @returns {string}\n     */\n    getFileView(bucketId: string, fileId: string): string {\n        if (typeof bucketId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"bucketId\"');\n        }\n        if (typeof fileId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"fileId\"');\n        }\n        const apiPath = '/storage/buckets/{bucketId}/files/{fileId}/view'.replace('{bucketId}', bucketId).replace('{fileId}', fileId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        payload['project'] = this.client.config.project;\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n\n        payload['project'] = this.client.config.project;\n\n        return uri.toString();\n    }\n}\n", "import { Service } from '../service';\nimport { AppwriteException, Client, type Payload, UploadProgress } from '../client';\nimport type { Models } from '../models';\n\nexport class Teams {\n    client: Client;\n\n    constructor(client: Client) {\n        this.client = client;\n    }\n\n    /**\n     * List teams\n     *\n     * Get a list of all the teams in which the current user is a member. You can use the parameters to filter your results.\n     *\n     * @param {string[]} queries\n     * @param {string} search\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.TeamList<Preferences>>}\n     */\n    async list<Preferences extends Models.Preferences>(queries?: string[], search?: string): Promise<Models.TeamList<Preferences>> {\n        const apiPath = '/teams';\n        const payload: Payload = {};\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n        if (typeof search !== 'undefined') {\n            payload['search'] = search;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * Create team\n     *\n     * Create a new team. The user who creates the team will automatically be assigned as the owner of the team. Only the users with the owner role can invite new members, add new owners and delete or update the team.\n     *\n     * @param {string} teamId\n     * @param {string} name\n     * @param {string[]} roles\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Team<Preferences>>}\n     */\n    async create<Preferences extends Models.Preferences>(teamId: string, name: string, roles?: string[]): Promise<Models.Team<Preferences>> {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        if (typeof name === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"name\"');\n        }\n        const apiPath = '/teams';\n        const payload: Payload = {};\n        if (typeof teamId !== 'undefined') {\n            payload['teamId'] = teamId;\n        }\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        if (typeof roles !== 'undefined') {\n            payload['roles'] = roles;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * Get team\n     *\n     * Get a team by its ID. All team members have read access for this resource.\n     *\n     * @param {string} teamId\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Team<Preferences>>}\n     */\n    async get<Preferences extends Models.Preferences>(teamId: string): Promise<Models.Team<Preferences>> {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        const apiPath = '/teams/{teamId}'.replace('{teamId}', teamId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * Update name\n     *\n     * Update the team&#039;s name by its unique ID.\n     *\n     * @param {string} teamId\n     * @param {string} name\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Team<Preferences>>}\n     */\n    async updateName<Preferences extends Models.Preferences>(teamId: string, name: string): Promise<Models.Team<Preferences>> {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        if (typeof name === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"name\"');\n        }\n        const apiPath = '/teams/{teamId}'.replace('{teamId}', teamId);\n        const payload: Payload = {};\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'put',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * Delete team\n     *\n     * Delete a team using its ID. Only team members with the owner role can delete the team.\n     *\n     * @param {string} teamId\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    async delete(teamId: string): Promise<{}> {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        const apiPath = '/teams/{teamId}'.replace('{teamId}', teamId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'delete',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * List team memberships\n     *\n     * Use this endpoint to list a team&#039;s members using the team&#039;s ID. All team members have read access to this endpoint. Hide sensitive attributes from the response by toggling membership privacy in the Console.\n     *\n     * @param {string} teamId\n     * @param {string[]} queries\n     * @param {string} search\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.MembershipList>}\n     */\n    async listMemberships(teamId: string, queries?: string[], search?: string): Promise<Models.MembershipList> {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        const apiPath = '/teams/{teamId}/memberships'.replace('{teamId}', teamId);\n        const payload: Payload = {};\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n        if (typeof search !== 'undefined') {\n            payload['search'] = search;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * Create team membership\n     *\n     * Invite a new member to join your team. Provide an ID for existing users, or invite unregistered users using an email or phone number. If initiated from a Client SDK, Appwrite will send an email or sms with a link to join the team to the invited user, and an account will be created for them if one doesn&#039;t exist. If initiated from a Server SDK, the new member will be added automatically to the team.\n\nYou only need to provide one of a user ID, email, or phone number. Appwrite will prioritize accepting the user ID &gt; email &gt; phone number if you provide more than one of these parameters.\n\nUse the `url` parameter to redirect the user from the invitation email to your app. After the user is redirected, use the [Update Team Membership Status](https://appwrite.io/docs/references/cloud/client-web/teams#updateMembershipStatus) endpoint to allow the user to accept the invitation to the team. \n\nPlease note that to avoid a [Redirect Attack](https://github.com/OWASP/CheatSheetSeries/blob/master/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.md) Appwrite will accept the only redirect URLs under the domains you have added as a platform on the Appwrite Console.\n\n     *\n     * @param {string} teamId\n     * @param {string[]} roles\n     * @param {string} email\n     * @param {string} userId\n     * @param {string} phone\n     * @param {string} url\n     * @param {string} name\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Membership>}\n     */\n    async createMembership(teamId: string, roles: string[], email?: string, userId?: string, phone?: string, url?: string, name?: string): Promise<Models.Membership> {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        if (typeof roles === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"roles\"');\n        }\n        const apiPath = '/teams/{teamId}/memberships'.replace('{teamId}', teamId);\n        const payload: Payload = {};\n        if (typeof email !== 'undefined') {\n            payload['email'] = email;\n        }\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof phone !== 'undefined') {\n            payload['phone'] = phone;\n        }\n        if (typeof roles !== 'undefined') {\n            payload['roles'] = roles;\n        }\n        if (typeof url !== 'undefined') {\n            payload['url'] = url;\n        }\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * Get team membership\n     *\n     * Get a team member by the membership unique id. All team members have read access for this resource. Hide sensitive attributes from the response by toggling membership privacy in the Console.\n     *\n     * @param {string} teamId\n     * @param {string} membershipId\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Membership>}\n     */\n    async getMembership(teamId: string, membershipId: string): Promise<Models.Membership> {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        if (typeof membershipId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"membershipId\"');\n        }\n        const apiPath = '/teams/{teamId}/memberships/{membershipId}'.replace('{teamId}', teamId).replace('{membershipId}', membershipId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * Update membership\n     *\n     * Modify the roles of a team member. Only team members with the owner role have access to this endpoint. Learn more about [roles and permissions](https://appwrite.io/docs/permissions).\n\n     *\n     * @param {string} teamId\n     * @param {string} membershipId\n     * @param {string[]} roles\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Membership>}\n     */\n    async updateMembership(teamId: string, membershipId: string, roles: string[]): Promise<Models.Membership> {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        if (typeof membershipId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"membershipId\"');\n        }\n        if (typeof roles === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"roles\"');\n        }\n        const apiPath = '/teams/{teamId}/memberships/{membershipId}'.replace('{teamId}', teamId).replace('{membershipId}', membershipId);\n        const payload: Payload = {};\n        if (typeof roles !== 'undefined') {\n            payload['roles'] = roles;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'patch',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * Delete team membership\n     *\n     * This endpoint allows a user to leave a team or for a team owner to delete the membership of any other team member. You can also use this endpoint to delete a user membership even if it is not accepted.\n     *\n     * @param {string} teamId\n     * @param {string} membershipId\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    async deleteMembership(teamId: string, membershipId: string): Promise<{}> {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        if (typeof membershipId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"membershipId\"');\n        }\n        const apiPath = '/teams/{teamId}/memberships/{membershipId}'.replace('{teamId}', teamId).replace('{membershipId}', membershipId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'delete',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * Update team membership status\n     *\n     * Use this endpoint to allow a user to accept an invitation to join a team after being redirected back to your app from the invitation email received by the user.\n\nIf the request is successful, a session for the user is automatically created.\n\n     *\n     * @param {string} teamId\n     * @param {string} membershipId\n     * @param {string} userId\n     * @param {string} secret\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Membership>}\n     */\n    async updateMembershipStatus(teamId: string, membershipId: string, userId: string, secret: string): Promise<Models.Membership> {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        if (typeof membershipId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"membershipId\"');\n        }\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n        if (typeof secret === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"secret\"');\n        }\n        const apiPath = '/teams/{teamId}/memberships/{membershipId}/status'.replace('{teamId}', teamId).replace('{membershipId}', membershipId);\n        const payload: Payload = {};\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof secret !== 'undefined') {\n            payload['secret'] = secret;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'patch',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * Get team preferences\n     *\n     * Get the team&#039;s shared preferences by its unique ID. If a preference doesn&#039;t need to be shared by all team members, prefer storing them in [user preferences](https://appwrite.io/docs/references/cloud/client-web/account#getPrefs).\n     *\n     * @param {string} teamId\n     * @throws {AppwriteException}\n     * @returns {Promise<Preferences>}\n     */\n    async getPrefs<Preferences extends Models.Preferences>(teamId: string): Promise<Preferences> {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        const apiPath = '/teams/{teamId}/prefs'.replace('{teamId}', teamId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * Update preferences\n     *\n     * Update the team&#039;s preferences by its unique ID. The object you pass is stored as is and replaces any previous value. The maximum allowed prefs size is 64kB and throws an error if exceeded.\n     *\n     * @param {string} teamId\n     * @param {object} prefs\n     * @throws {AppwriteException}\n     * @returns {Promise<Preferences>}\n     */\n    async updatePrefs<Preferences extends Models.Preferences>(teamId: string, prefs: object): Promise<Preferences> {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        if (typeof prefs === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"prefs\"');\n        }\n        const apiPath = '/teams/{teamId}/prefs'.replace('{teamId}', teamId);\n        const payload: Payload = {};\n        if (typeof prefs !== 'undefined') {\n            payload['prefs'] = prefs;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n\n        return await this.client.call(\n            'put',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n}\n", "/**\n * Helper class to generate permission strings for resources.\n */\nexport class Permission {\n    /**\n     * Generate read permission string for the provided role.\n     *\n     * @param {string} role\n     * @returns {string}\n     */\n    static read = (role: string): string => {\n        return `read(\"${role}\")`;\n    }\n\n    /**\n     * Generate write permission string for the provided role.\n     *\n     * This is an alias of update, delete, and possibly create.\n     * Don't use write in combination with update, delete, or create.\n     *\n     * @param {string} role\n     * @returns {string}\n     */\n    static write = (role: string): string => {\n        return `write(\"${role}\")`;\n    }\n\n    /**\n     * Generate create permission string for the provided role.\n     *\n     * @param {string} role\n     * @returns {string}\n     */\n    static create = (role: string): string => {\n        return `create(\"${role}\")`;\n    }\n\n    /**\n     * Generate update permission string for the provided role.\n     *\n     * @param {string} role\n     * @returns {string}\n     */\n    static update = (role: string): string => {\n        return `update(\"${role}\")`;\n    }\n\n    /**\n     * Generate delete permission string for the provided role.\n     *\n     * @param {string} role\n     * @returns {string}\n     */\n    static delete = (role: string): string => {\n        return `delete(\"${role}\")`;\n    }\n}\n", "/**\n * Helper class to generate role strings for `Permission`.\n */\nexport class Role {\n\n    /**\n     * Grants access to anyone.\n     * \n     * This includes authenticated and unauthenticated users.\n     * \n     * @returns {string}\n     */\n    public static any(): string {\n        return 'any'\n    }\n\n    /**\n     * Grants access to a specific user by user ID.\n     * \n     * You can optionally pass verified or unverified for\n     * `status` to target specific types of users.\n     *\n     * @param {string} id \n     * @param {string} status \n     * @returns {string}\n     */\n    public static user(id: string, status: string = ''): string {\n        if (status === '') {\n            return `user:${id}`\n        }\n        return `user:${id}/${status}`\n    }\n\n    /**\n     * Grants access to any authenticated or anonymous user.\n     * \n     * You can optionally pass verified or unverified for\n     * `status` to target specific types of users.\n     * \n     * @param {string} status \n     * @returns {string}\n     */\n    public static users(status: string = ''): string {\n        if (status === '') {\n            return 'users'\n        }\n        return `users/${status}`\n    }\n\n    /**\n     * Grants access to any guest user without a session.\n     * \n     * Authenticated users don't have access to this role.\n     * \n     * @returns {string}\n     */\n    public static guests(): string {\n        return 'guests'\n    }\n\n    /**\n     * Grants access to a team by team ID.\n     * \n     * You can optionally pass a role for `role` to target\n     * team members with the specified role.\n     * \n     * @param {string} id \n     * @param {string} role \n     * @returns {string}\n     */\n    public static team(id: string, role: string = ''): string {\n        if (role === '') {\n            return `team:${id}`\n        }\n        return `team:${id}/${role}`\n    }\n\n    /**\n     * Grants access to a specific member of a team.\n     * \n     * When the member is removed from the team, they will\n     * no longer have access.\n     * \n     * @param {string} id \n     * @returns {string}\n     */\n    public static member(id: string): string {\n        return `member:${id}`\n    }\n\n    /**\n     * Grants access to a user with the specified label.\n     * \n     * @param {string} name \n     * @returns  {string}\n     */\n    public static label(name: string): string {\n        return `label:${name}`\n    }\n}", "/**\n * Helper class to generate ID strings for resources.\n */\nexport class ID {\n    /**\n     * Generate an hex ID based on timestamp.\n     * Recreated from https://www.php.net/manual/en/function.uniqid.php\n     *\n     * @returns {string}\n     */\n    static #hexTimestamp(): string {\n        const now = new Date();\n        const sec = Math.floor(now.getTime() / 1000);\n        const msec = now.getMilliseconds();\n\n        // Convert to hexadecimal\n        const hexTimestamp = sec.toString(16) + msec.toString(16).padStart(5, '0');\n        return hexTimestamp;\n    }\n\n    /**\n     * Uses the provided ID as the ID for the resource.\n     *\n     * @param {string} id\n     * @returns {string}\n     */\n    public static custom(id: string): string {\n        return id\n    }\n\n    /**\n     * Have Appwrite generate a unique ID for you.\n     * \n     * @param {number} padding. Default is 7.\n     * @returns {string}\n     */\n    public static unique(padding: number = 7): string {\n        // Generate a unique ID with padding to have a longer ID\n        const baseId = ID.#hexTimestamp();\n        let randomPadding = '';\n        for (let i = 0; i < padding; i++) {\n            const randomHexDigit = Math.floor(Math.random() * 16).toString(16);\n            randomPadding += randomHexDigit;\n        }\n        return baseId + randomPadding;\n    }\n}\n", "export enum AuthenticatorType {\n    Totp = 'totp',\n}", "export enum AuthenticationFactor {\n    Email = 'email',\n    Phone = 'phone',\n    Totp = 'totp',\n    Recoverycode = 'recoverycode',\n}", "export enum OAuthProvider {\n    Amazon = 'amazon',\n    Apple = 'apple',\n    Auth0 = 'auth0',\n    Authentik = 'authentik',\n    Autodesk = 'autodesk',\n    Bitbucket = 'bitbucket',\n    Bitly = 'bitly',\n    Box = 'box',\n    Dailymotion = 'dailymotion',\n    Discord = 'discord',\n    Disqus = 'disqus',\n    Dropbox = 'dropbox',\n    Etsy = 'etsy',\n    Facebook = 'facebook',\n    Github = 'github',\n    Gitlab = 'gitlab',\n    Google = 'google',\n    Linkedin = 'linkedin',\n    Microsoft = 'microsoft',\n    Notion = 'notion',\n    Oidc = 'oidc',\n    Okta = 'okta',\n    Paypal = 'paypal',\n    PaypalSandbox = 'paypalSandbox',\n    Podio = 'podio',\n    Salesforce = 'salesforce',\n    Slack = 'slack',\n    Spotify = 'spotify',\n    Stripe = 'stripe',\n    Tradeshift = 'tradeshift',\n    TradeshiftBox = 'tradeshiftBox',\n    Twitch = 'twitch',\n    Wordpress = 'wordpress',\n    Yahoo = 'yahoo',\n    Yammer = 'yammer',\n    Yandex = 'yandex',\n    Zoho = 'zoho',\n    Zoom = 'zoom',\n    Mock = 'mock',\n}", "export enum Browser {\n    AvantBrowser = 'aa',\n    AndroidWebViewBeta = 'an',\n    GoogleChrome = 'ch',\n    GoogleChromeIOS = 'ci',\n    GoogleChromeMobile = 'cm',\n    Chromium = 'cr',\n    MozillaFirefox = 'ff',\n    Safari = 'sf',\n    MobileSafari = 'mf',\n    MicrosoftEdge = 'ps',\n    MicrosoftEdgeIOS = 'oi',\n    OperaMini = 'om',\n    Opera = 'op',\n    OperaNext = 'on',\n}", "export enum CreditCard {\n    AmericanExpress = 'amex',\n    Argencard = 'argencard',\n    Cabal = 'cabal',\n    Cencosud = 'cencosud',\n    DinersClub = 'diners',\n    Discover = 'discover',\n    Elo = 'elo',\n    Hipercard = 'hipercard',\n    JCB = 'jcb',\n    Mastercard = 'mastercard',\n    Naranja = 'naranja',\n    TarjetaShopping = 'targeta-shopping',\n    UnionChinaPay = 'union-china-pay',\n    Visa = 'visa',\n    MIR = 'mir',\n    Mae<PERSON> = 'maestro',\n}", "export enum Flag {\n    Afghanistan = 'af',\n    Angola = 'ao',\n    Albania = 'al',\n    Andorra = 'ad',\n    UnitedArabEmirates = 'ae',\n    Argentina = 'ar',\n    Armenia = 'am',\n    AntiguaAndBarbuda = 'ag',\n    Australia = 'au',\n    Austria = 'at',\n    Azerbaijan = 'az',\n    Burundi = 'bi',\n    Belgium = 'be',\n    Benin = 'bj',\n    BurkinaFaso = 'bf',\n    Bangladesh = 'bd',\n    Bulgaria = 'bg',\n    Bahrain = 'bh',\n    Bahamas = 'bs',\n    BosniaAndHerzegovina = 'ba',\n    Belarus = 'by',\n    Belize = 'bz',\n    Bolivia = 'bo',\n    Brazil = 'br',\n    Barbados = 'bb',\n    BruneiDarussalam = 'bn',\n    Bhutan = 'bt',\n    Botswana = 'bw',\n    CentralAfricanRepublic = 'cf',\n    Canada = 'ca',\n    Switzerland = 'ch',\n    Chile = 'cl',\n    China = 'cn',\n    CoteDIvoire = 'ci',\n    Cameroon = 'cm',\n    DemocraticRepublicOfTheCongo = 'cd',\n    RepublicOfTheCongo = 'cg',\n    Colombia = 'co',\n    Comoros = 'km',\n    CapeVerde = 'cv',\n    CostaRica = 'cr',\n    Cuba = 'cu',\n    Cyprus = 'cy',\n    CzechRepublic = 'cz',\n    Germany = 'de',\n    Djibouti = 'dj',\n    Dominica = 'dm',\n    Denmark = 'dk',\n    DominicanRepublic = 'do',\n    Algeria = 'dz',\n    Ecuador = 'ec',\n    Egypt = 'eg',\n    Eritrea = 'er',\n    Spain = 'es',\n    Estonia = 'ee',\n    Ethiopia = 'et',\n    Finland = 'fi',\n    Fiji = 'fj',\n    France = 'fr',\n    MicronesiaFederatedStatesOf = 'fm',\n    Gabon = 'ga',\n    UnitedKingdom = 'gb',\n    Georgia = 'ge',\n    Ghana = 'gh',\n    Guinea = 'gn',\n    Gambia = 'gm',\n    GuineaBissau = 'gw',\n    EquatorialGuinea = 'gq',\n    Greece = 'gr',\n    Grenada = 'gd',\n    Guatemala = 'gt',\n    Guyana = 'gy',\n    Honduras = 'hn',\n    Croatia = 'hr',\n    Haiti = 'ht',\n    Hungary = 'hu',\n    Indonesia = 'id',\n    India = 'in',\n    Ireland = 'ie',\n    IranIslamicRepublicOf = 'ir',\n    Iraq = 'iq',\n    Iceland = 'is',\n    Israel = 'il',\n    Italy = 'it',\n    Jamaica = 'jm',\n    Jordan = 'jo',\n    Japan = 'jp',\n    Kazakhstan = 'kz',\n    Kenya = 'ke',\n    Kyrgyzstan = 'kg',\n    Cambodia = 'kh',\n    Kiribati = 'ki',\n    SaintKittsAndNevis = 'kn',\n    SouthKorea = 'kr',\n    Kuwait = 'kw',\n    LaoPeopleSDemocraticRepublic = 'la',\n    Lebanon = 'lb',\n    Liberia = 'lr',\n    Libya = 'ly',\n    SaintLucia = 'lc',\n    Liechtenstein = 'li',\n    SriLanka = 'lk',\n    Lesotho = 'ls',\n    Lithuania = 'lt',\n    Luxembourg = 'lu',\n    Latvia = 'lv',\n    Morocco = 'ma',\n    Monaco = 'mc',\n    Moldova = 'md',\n    Madagascar = 'mg',\n    Maldives = 'mv',\n    Mexico = 'mx',\n    MarshallIslands = 'mh',\n    NorthMacedonia = 'mk',\n    Mali = 'ml',\n    Malta = 'mt',\n    Myanmar = 'mm',\n    Montenegro = 'me',\n    Mongolia = 'mn',\n    Mozambique = 'mz',\n    Mauritania = 'mr',\n    Mauritius = 'mu',\n    Malawi = 'mw',\n    Malaysia = 'my',\n    Namibia = 'na',\n    Niger = 'ne',\n    Nigeria = 'ng',\n    Nicaragua = 'ni',\n    Netherlands = 'nl',\n    Norway = 'no',\n    Nepal = 'np',\n    Nauru = 'nr',\n    NewZealand = 'nz',\n    Oman = 'om',\n    Pakistan = 'pk',\n    Panama = 'pa',\n    Peru = 'pe',\n    Philippines = 'ph',\n    Palau = 'pw',\n    PapuaNewGuinea = 'pg',\n    Poland = 'pl',\n    FrenchPolynesia = 'pf',\n    NorthKorea = 'kp',\n    Portugal = 'pt',\n    Paraguay = 'py',\n    Qatar = 'qa',\n    Romania = 'ro',\n    Russia = 'ru',\n    Rwanda = 'rw',\n    SaudiArabia = 'sa',\n    Sudan = 'sd',\n    Senegal = 'sn',\n    Singapore = 'sg',\n    SolomonIslands = 'sb',\n    SierraLeone = 'sl',\n    ElSalvador = 'sv',\n    SanMarino = 'sm',\n    Somalia = 'so',\n    Serbia = 'rs',\n    SouthSudan = 'ss',\n    SaoTomeAndPrincipe = 'st',\n    Suriname = 'sr',\n    Slovakia = 'sk',\n    Slovenia = 'si',\n    Sweden = 'se',\n    Eswatini = 'sz',\n    Seychelles = 'sc',\n    Syria = 'sy',\n    Chad = 'td',\n    Togo = 'tg',\n    Thailand = 'th',\n    Tajikistan = 'tj',\n    Turkmenistan = 'tm',\n    TimorLeste = 'tl',\n    Tonga = 'to',\n    TrinidadAndTobago = 'tt',\n    Tunisia = 'tn',\n    Turkey = 'tr',\n    Tuvalu = 'tv',\n    Tanzania = 'tz',\n    Uganda = 'ug',\n    Ukraine = 'ua',\n    Uruguay = 'uy',\n    UnitedStates = 'us',\n    Uzbekistan = 'uz',\n    VaticanCity = 'va',\n    SaintVincentAndTheGrenadines = 'vc',\n    Venezuela = 've',\n    Vietnam = 'vn',\n    Vanuatu = 'vu',\n    Samoa = 'ws',\n    Yemen = 'ye',\n    SouthAfrica = 'za',\n    Zambia = 'zm',\n    Zimbabwe = 'zw',\n}", "export enum ExecutionMethod {\n    GET = 'GET',\n    POST = 'POST',\n    PUT = 'PUT',\n    PATCH = 'PATCH',\n    DELETE = 'DELETE',\n    OPTIONS = 'OPTIONS',\n}", "export enum ImageGravity {\n    Center = 'center',\n    Topleft = 'top-left',\n    Top = 'top',\n    Topright = 'top-right',\n    Left = 'left',\n    Right = 'right',\n    Bottomleft = 'bottom-left',\n    Bottom = 'bottom',\n    Bottomright = 'bottom-right',\n}", "export enum ImageFormat {\n    Jpg = 'jpg',\n    Jpeg = 'jpeg',\n    Gif = 'gif',\n    Png = 'png',\n    Webp = 'webp',\n    Heic = 'heic',\n    Avif = 'avif',\n}"], "mappings": ";AAqEO,SAAS,UAAU,SAAS,YAAY,GAAG,WAAW;AACzD,WAAS,MAAM,OAAO;AAAE,WAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,SAAS;AAAE,cAAQ,KAAK;IAAE,CAAE;EAAE;AAC1G,SAAO,KAAK,MAAM,IAAI,UAAU,SAAU,SAAS,QAAQ;AACvD,aAAS,UAAU,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,KAAK,KAAK,CAAC;MAAE,SAAU,GAAG;AAAE,eAAO,CAAC;MAAE;IAAE;AACzF,aAAS,SAAS,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,OAAO,EAAE,KAAK,CAAC;MAAE,SAAU,GAAG;AAAE,eAAO,CAAC;MAAE;IAAE;AAC5F,aAAS,KAAK,QAAQ;AAAE,aAAO,OAAO,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,KAAK,WAAW,QAAQ;IAAE;AAC5G,UAAM,YAAY,UAAU,MAAM,SAAS,cAAc,CAAA,CAAE,GAAG,KAAI,CAAE;EAC5E,CAAK;AACL;AA0JO,SAAS,uBAAuB,UAAU,OAAO,MAAM,GAAG;AAC7D,MAAI,SAAS,OAAO,CAAC,EAAG,OAAM,IAAI,UAAU,+CAA+C;AAC3F,MAAI,OAAO,UAAU,aAAa,aAAa,SAAS,CAAC,IAAI,CAAC,MAAM,IAAI,QAAQ,EAAG,OAAM,IAAI,UAAU,0EAA0E;AACjL,SAAO,SAAS,MAAM,IAAI,SAAS,MAAM,EAAE,KAAK,QAAQ,IAAI,IAAI,EAAE,QAAQ,MAAM,IAAI,QAAQ;AAChG;ICnOa,cAAK;;;;;;;;EAYhB,YACE,QACA,WACA,QAAmB;AAEnB,SAAK,SAAS;AACd,SAAK,YAAY;AAEjB,QAAI,WAAW,QAAW;AACxB,UAAI,MAAM,QAAQ,MAAM,GAAG;AACzB,aAAK,SAAS;MACf,OAAM;AACL,aAAK,SAAS,CAAC,MAAM;MACtB;IACF;;;;;;;EAQH,WAAQ;AACN,WAAO,KAAK,UAAU;MACpB,QAAQ,KAAK;MACb,WAAW,KAAK;MAChB,QAAQ,KAAK;IACd,CAAA;;;AAUI,MAAK,QAAG,CAAC,WAAmB,UACjC,IAAI,MAAM,SAAS,WAAW,KAAK,EAAE,SAAQ;AASxC,MAAQ,WAAG,CAAC,WAAmB,UACpC,IAAI,MAAM,YAAY,WAAW,KAAK,EAAE,SAAQ;AAS3C,MAAQ,WAAG,CAAC,WAAmB,UACpC,IAAI,MAAM,YAAY,WAAW,KAAK,EAAE,SAAQ;AAS3C,MAAa,gBAAG,CAAC,WAAmB,UACzC,IAAI,MAAM,iBAAiB,WAAW,KAAK,EAAE,SAAQ;AAShD,MAAW,cAAG,CAAC,WAAmB,UACvC,IAAI,MAAM,eAAe,WAAW,KAAK,EAAE,SAAQ;AAS9C,MAAgB,mBAAG,CAAC,WAAmB,UAC5C,IAAI,MAAM,oBAAoB,WAAW,KAAK,EAAE,SAAQ;AAQnD,MAAA,SAAS,CAAC,cACf,IAAI,MAAM,UAAU,SAAS,EAAE,SAAQ;AAQlC,MAAA,YAAY,CAAC,cAClB,IAAI,MAAM,aAAa,SAAS,EAAE,SAAQ;AAUrC,MAAO,UAAG,CAAC,WAAmB,OAAwB,QAC3D,IAAI,MAAM,WAAW,WAAW,CAAC,OAAO,GAAG,CAAmB,EAAE,SAAQ;AASnE,MAAU,aAAG,CAAC,WAAmB,UACtC,IAAI,MAAM,cAAc,WAAW,KAAK,EAAE,SAAQ;AAS7C,MAAQ,WAAG,CAAC,WAAmB,UACpC,IAAI,MAAM,YAAY,WAAW,KAAK,EAAE,SAAQ;AAQ3C,MAAA,SAAS,CAAC,eACf,IAAI,MAAM,UAAU,QAAW,UAAU,EAAE,SAAQ;AAU9C,MAAM,SAAG,CAAC,WAAmB,UAClC,IAAI,MAAM,UAAU,WAAW,KAAK,EAAE,SAAQ;AAQzC,MAAA,YAAY,CAAC,cAClB,IAAI,MAAM,aAAa,SAAS,EAAE,SAAQ;AAQrC,MAAA,WAAW,CAAC,cACjB,IAAI,MAAM,YAAY,SAAS,EAAE,SAAQ;AAQpC,MAAA,cAAc,CAAC,eACpB,IAAI,MAAM,eAAe,QAAW,UAAU,EAAE,SAAQ;AAQnD,MAAA,eAAe,CAAC,eACrB,IAAI,MAAM,gBAAgB,QAAW,UAAU,EAAE,SAAQ;AAQpD,MAAA,QAAQ,CAAC,UACd,IAAI,MAAM,SAAS,QAAW,KAAK,EAAE,SAAQ;AAQxC,MAAA,SAAS,CAAC,WACf,IAAI,MAAM,UAAU,QAAW,MAAM,EAAE,SAAQ;AAS1C,MAAQ,WAAG,CAAC,WAAmB,UACpC,IAAI,MAAM,YAAY,WAAW,KAAK,EAAE,SAAQ;AAQ3C,MAAA,KAAK,CAAC,YACX,IAAI,MAAM,MAAM,QAAW,QAAQ,IAAI,CAAC,UAAU,KAAK,MAAM,KAAK,CAAC,CAAC,EAAE,SAAQ;AAQzE,MAAA,MAAM,CAAC,YACZ,IAAI,MAAM,OAAO,QAAW,QAAQ,IAAI,CAAC,UAAU,KAAK,MAAM,KAAK,CAAC,CAAC,EAAE,SAAQ;ACDnF,IAAM,oBAAN,cAAgC,MAAK;;;;;;;;;EAyBjC,YAAY,SAAiB,OAAe,GAAG,OAAe,IAAI,WAAmB,IAAE;AACnF,UAAM,OAAO;AACb,SAAK,OAAO;AACZ,SAAK,UAAU;AACf,SAAK,OAAO;AACZ,SAAK,OAAO;AACZ,SAAK,WAAW;;AAEvB;AAKD,IAAM,SAAN,MAAM,QAAM;EAAZ,cAAA;AAMI,SAAA,SAAS;MACL,UAAU;MACV,kBAAkB;MAClB,SAAS;MACT,KAAK;MACL,QAAQ;MACR,SAAS;;AAKb,SAAA,UAAmB;MACf,cAAc;MACd,kBAAkB;MAClB,kBAAkB;MAClB,iBAAiB;MACjB,8BAA8B;;AAuF1B,SAAA,WAAqB;MACzB,QAAQ;MACR,SAAS;MACT,WAAW;MACX,KAAK;MACL,UAAU,oBAAI,IAAG;MACjB,eAAe,oBAAI,IAAG;MACtB,sBAAsB;MACtB,WAAW;MACX,mBAAmB;MACnB,aAAa;MACb,SAAS,MAAK;AACV,qBAAa,KAAK,SAAS,OAAO;AAClC,aAAK,SAAS,UAAU,WAAM,QAAN,WAAM,SAAA,SAAN,OAAQ,WAAW,MAAK;AAC5C,eAAK,SAAS,aAAY;WAC3B,EAAE;;MAET,YAAY,MAAK;AACb,gBAAQ,MAAI;UACR,KAAK,KAAK,SAAS,oBAAoB;AACnC,mBAAO;UACX,KAAK,KAAK,SAAS,oBAAoB;AACnC,mBAAO;UACX,KAAK,KAAK,SAAS,oBAAoB;AACnC,mBAAO;UACX;AACI,mBAAO;QACd;;MAEL,iBAAiB,MAAK;AAClB,YAAI,KAAK,SAAS,WAAW;AACzB,uBAAa,KAAK,SAAS,SAAS;QACvC;AAED,aAAK,SAAS,YAAY,WAAM,QAAN,WAAM,SAAA,SAAN,OAAQ,YAAY,MAAK;;AAC/C,WAAAA,MAAA,KAAK,SAAS,YAAQ,QAAAA,QAAA,SAAA,SAAAA,IAAA,KAAK,KAAK,UAAU;YACtC,MAAM;UACT,CAAA,CAAC;WACH,GAAM;;MAEb,cAAc,MAAK;;AACf,YAAI,KAAK,SAAS,SAAS,OAAO,GAAG;AACjC,eAAK,SAAS,YAAY;AAC1B,WAAAA,MAAA,KAAK,SAAS,YAAQ,QAAAA,QAAA,SAAA,SAAAA,IAAA,MAAK;AAC3B;QACH;AAED,cAAM,WAAW,IAAI,gBAAe;AACpC,iBAAS,IAAI,WAAW,KAAK,OAAO,OAAO;AAC3C,aAAK,SAAS,SAAS,QAAQ,aAAU;AACrC,mBAAS,OAAO,cAAc,OAAO;QACzC,CAAC;AAED,cAAM,MAAM,KAAK,OAAO,mBAAmB,eAAe,SAAS,SAAQ;AAE3E,YACI,QAAQ,KAAK,SAAS;QACtB,CAAC,KAAK,SAAS;UACf,KAAA,KAAK,SAAS,YAAQ,QAAA,OAAA,SAAA,SAAA,GAAA,cAAa,UAAU,MAC/C;AACE,cACI,KAAK,SAAS,YACd,KAAA,KAAK,SAAS,YAAQ,QAAA,OAAA,SAAA,SAAA,GAAA,cAAa,UAAU,SAC/C;AACE,iBAAK,SAAS,YAAY;AAC1B,iBAAK,SAAS,OAAO,MAAK;UAC7B;AAED,eAAK,SAAS,MAAM;AACpB,eAAK,SAAS,SAAS,IAAI,UAAU,GAAG;AACxC,eAAK,SAAS,OAAO,iBAAiB,WAAW,KAAK,SAAS,SAAS;AACxE,eAAK,SAAS,OAAO,iBAAiB,QAAQ,YAAS;AACnD,iBAAK,SAAS,oBAAoB;AAClC,iBAAK,SAAS,gBAAe;UACjC,CAAC;AACD,eAAK,SAAS,OAAO,iBAAiB,SAAS,WAAQ;;AACnD,gBACI,CAAC,KAAK,SAAS,eAEXC,OAAAD,MAAA,KAAK,cAAU,QAAAA,QAAA,SAAA,SAAAA,IAAA,iBAAa,QAAAC,QAAA,SAAA,SAAAA,IAAA,UAAS;cACbC,MAAA,KAAK,cAAQ,QAAAA,QAAA,SAAA,SAAAA,IAAE,YAAY,MAAM,SAAS,MAExE;AACE,mBAAK,SAAS,YAAY;AAC1B;YACH;AAED,kBAAM,UAAU,KAAK,SAAS,WAAU;AACxC,oBAAQ,MAAM,6DAA6D,UAAU,GAAI,aAAa,MAAM,MAAM;AAElH,uBAAW,MAAK;AACZ,mBAAK,SAAS;AACd,mBAAK,SAAS,aAAY;eAC3B,OAAO;UACd,CAAC;QACJ;;MAEL,WAAW,CAAC,UAAS;;AACjB,YAAI;AACA,gBAAM,UAA4B,KAAK,MAAM,MAAM,IAAI;AACvD,eAAK,SAAS,cAAc;AAC5B,kBAAQ,QAAQ,MAAI;YAChB,KAAK;AACD,oBAAM,SAAS,KAAK,OAAMF,MAAA,OAAO,aAAa,QAAQ,gBAAgB,OAAK,QAAAA,QAAA,SAAAA,MAAA,IAAI;AAC/E,oBAAM,UAAU,WAAA,QAAA,WAAM,SAAA,SAAN,OAAS,aAAa,KAAK,OAAO,OAAO,EAAE;AAC3D,oBAAM,cAAyC,QAAQ;AAEvD,kBAAI,WAAW,CAAC,YAAY,MAAM;AAC9B,iBAAA,KAAA,KAAK,SAAS,YAAQ,QAAA,OAAA,SAAA,SAAA,GAAA,KAAK,KAAK,UAA2B;kBACvD,MAAM;kBACN,MAAM;oBACF;kBACH;gBACJ,CAAA,CAAC;cACL;AACD;YACJ,KAAK;AACD,kBAAI,OAAuC,QAAQ;AACnD,kBAAI,SAAA,QAAA,SAAI,SAAA,SAAJ,KAAM,UAAU;AAChB,sBAAM,eAAe,KAAK,SAAS,KAAK,aAAW,KAAK,SAAS,SAAS,IAAI,OAAO,CAAC;AACtF,oBAAI,CAAC;AAAc;AACnB,qBAAK,SAAS,cAAc,QAAQ,kBAAe;AAC/C,sBAAI,KAAK,SAAS,KAAK,aAAW,aAAa,SAAS,SAAS,OAAO,CAAC,GAAG;AACxE,+BAAW,MAAM,aAAa,SAAS,IAAI,CAAC;kBAC/C;gBACL,CAAC;cACJ;AACD;YACJ,KAAK;AACD;YACJ,KAAK;AACD,oBAAM,QAAQ;YAClB;AACI;UACP;QACJ,SAAQ,GAAG;AACR,kBAAQ,MAAM,CAAC;QAClB;;MAEL,SAAS,cAAW;AAChB,aAAK,SAAS,SAAS,QAAQ,aAAU;AACrC,cAAI,SAAS,SAAS,OAAO,GAAG;AAC5B,gBAAI,QAAQ,MAAM,KAAK,KAAK,SAAS,aAAa,EAAE,KAAK,CAAC,CAAC,MAAM,YAAY,MAAK;AAC9E,qBAAO,aAAa,SAAS,SAAS,OAAO;YACjD,CAAC;AAED,gBAAI,CAAC,OAAO;AACR,mBAAK,SAAS,SAAS,OAAO,OAAO;YACxC;UACJ;QACL,CAAC;;;;;;;;;;;;;EAjOT,YAAY,UAAgB;AACxB,SAAK,OAAO,WAAW;AACvB,SAAK,OAAO,mBAAmB,KAAK,OAAO,oBAAoB,KAAK,OAAO,SAAS,QAAQ,YAAY,QAAQ,EAAE,QAAQ,WAAW,OAAO;AAE5I,WAAO;;;;;;;;;EAUX,oBAAoB,kBAAwB;AACxC,SAAK,OAAO,mBAAmB;AAE/B,WAAO;;;;;;;;;;;EAYX,WAAW,OAAa;AACpB,SAAK,QAAQ,oBAAoB,IAAI;AACrC,SAAK,OAAO,UAAU;AACtB,WAAO;;;;;;;;;;;EAWX,OAAO,OAAa;AAChB,SAAK,QAAQ,gBAAgB,IAAI;AACjC,SAAK,OAAO,MAAM;AAClB,WAAO;;;;;;;;;EASX,UAAU,OAAa;AACnB,SAAK,QAAQ,mBAAmB,IAAI;AACpC,SAAK,OAAO,SAAS;AACrB,WAAO;;;;;;;;;;;EAWX,WAAW,OAAa;AACpB,SAAK,QAAQ,oBAAoB,IAAI;AACrC,SAAK,OAAO,UAAU;AACtB,WAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;EAsLX,UAA6B,UAA6B,UAAqD;AAC3G,QAAI,eAAe,OAAO,aAAa,WAAW,CAAC,QAAQ,IAAI;AAC/D,iBAAa,QAAQ,aAAW,KAAK,SAAS,SAAS,IAAI,OAAO,CAAC;AAEnE,UAAM,UAAU,KAAK,SAAS;AAC9B,SAAK,SAAS,cAAc,IAAI,SAAS;MACrC,UAAU;MACV;IACH,CAAA;AAED,SAAK,SAAS,QAAO;AAErB,WAAO,MAAK;AACR,WAAK,SAAS,cAAc,OAAO,OAAO;AAC1C,WAAK,SAAS,QAAQ,YAAY;AAClC,WAAK,SAAS,QAAO;IACzB;;EAGJ,eAAe,QAAgB,KAAU,UAAmB,CAAA,GAAI,SAAkB,CAAA,GAAE;AAChF,aAAS,OAAO,YAAW;AAE3B,cAAU,OAAO,OAAO,CAAA,GAAI,KAAK,SAAS,OAAO;AAEjD,QAAI,OAAO,WAAW,eAAe,OAAO,cAAc;AACtD,YAAM,iBAAiB,OAAO,aAAa,QAAQ,gBAAgB;AACnE,UAAI,gBAAgB;AAChB,gBAAQ,oBAAoB,IAAI;MACnC;IACJ;AAED,QAAI,UAAuB;MACvB;MACA;MACA,aAAa;;AAGjB,QAAI,WAAW,OAAO;AAClB,iBAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,QAAO,QAAQ,MAAM,CAAC,GAAG;AAC/D,YAAI,aAAa,OAAO,KAAK,KAAK;MACrC;IACJ,OAAM;AACH,cAAQ,QAAQ,cAAc,GAAC;QAC3B,KAAK;AACD,kBAAQ,OAAO,KAAK,UAAU,MAAM;AACpC;QAEJ,KAAK;AACD,gBAAM,WAAW,IAAI,SAAQ;AAE7B,qBAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,MAAM,GAAG;AAC/C,gBAAI,iBAAiB,MAAM;AACvB,uBAAS,OAAO,KAAK,OAAO,MAAM,IAAI;YACzC,WAAU,MAAM,QAAQ,KAAK,GAAG;AAC7B,yBAAW,eAAe,OAAO;AAC7B,yBAAS,OAAO,GAAG,GAAG,MAAM,WAAW;cAC1C;YACJ,OAAM;AACH,uBAAS,OAAO,KAAK,KAAK;YAC7B;UACJ;AAED,kBAAQ,OAAO;AACf,iBAAO,QAAQ,cAAc;AAC7B;MACP;IACJ;AAED,WAAO,EAAE,KAAK,IAAI,SAAQ,GAAI,QAAO;;EAGnC,cAAc,QAAgB,KAAU,UAAmB,CAAA,GAAI,kBAA2B,CAAA,GAAI,YAA8C;;AAC9I,YAAM,OAAO,OAAO,OAAO,eAAe,EAAE,KAAK,CAAC,UAAU,iBAAiB,IAAI;AAEjF,UAAI,KAAK,QAAQ,QAAO,YAAY;AAChC,eAAO,MAAM,KAAK,KAAK,QAAQ,KAAK,SAAS,eAAe;MAC/D;AAED,UAAI,QAAQ;AACZ,UAAI,WAAW;AAEf,aAAO,QAAQ,KAAK,MAAM;AACtB,YAAI,MAAM,QAAQ,QAAO;AACzB,YAAI,OAAO,KAAK,MAAM;AAClB,gBAAM,KAAK;QACd;AAED,gBAAQ,eAAe,IAAI,SAAS,KAAK,IAAI,MAAI,CAAC,IAAI,KAAK,IAAI;AAC/D,cAAM,QAAQ,KAAK,MAAM,OAAO,GAAG;AAEnC,YAAI,UAAe,OAAA,OAAA,OAAA,OAAA,CAAA,GAAA,eAAe,GAAA,EAAE,MAAM,IAAI,KAAK,CAAC,KAAK,GAAG,KAAK,IAAI,EAAC,CAAA;AAEtE,mBAAW,MAAM,KAAK,KAAK,QAAQ,KAAK,SAAS,OAAO;AAExD,YAAI,cAAc,OAAO,eAAe,YAAY;AAChD,qBAAW;YACP,KAAK,SAAS;YACd,UAAU,KAAK,MAAO,MAAM,KAAK,OAAQ,GAAG;YAC5C,cAAc;YACd,aAAa,KAAK,KAAK,KAAK,OAAO,QAAO,UAAU;YACpD,gBAAgB,KAAK,KAAK,MAAM,QAAO,UAAU;UACpD,CAAA;QACJ;AAED,YAAI,YAAY,SAAS,KAAK;AAC1B,kBAAQ,eAAe,IAAI,SAAS;QACvC;AAED,gBAAQ;MACX;AAED,aAAO;KACV;EAAA;EAEK,OAAI;;AACN,aAAO,KAAK,KAAK,OAAO,IAAI,IAAI,KAAK,OAAO,WAAW,OAAO,CAAC;KAClE;EAAA;EAEK,KAAK,QAAgB,KAAU,UAAmB,CAAA,GAAI,SAAkB,CAAA,GAAI,eAAe,QAAM;;;AACnG,YAAM,EAAE,KAAK,QAAO,IAAK,KAAK,eAAe,QAAQ,KAAK,SAAS,MAAM;AAEzE,UAAI,OAAY;AAEhB,YAAM,WAAW,MAAM,MAAM,KAAK,OAAO;AAEzC,YAAM,WAAW,SAAS,QAAQ,IAAI,oBAAoB;AAC1D,UAAI,UAAU;AACV,iBAAS,MAAM,GAAG,EAAE,QAAQ,CAAC,YAAoB,QAAQ,KAAK,cAAc,OAAO,CAAC;MACvF;AAED,WAAIA,MAAA,SAAS,QAAQ,IAAI,cAAc,OAAG,QAAAA,QAAA,SAAA,SAAAA,IAAA,SAAS,kBAAkB,GAAG;AACpE,eAAO,MAAM,SAAS,KAAI;MAC7B,WAAU,iBAAiB,eAAe;AACvC,eAAO,MAAM,SAAS,YAAW;MACpC,OAAM;AACH,eAAO;UACH,SAAS,MAAM,SAAS,KAAI;;MAEnC;AAED,UAAI,OAAO,SAAS,QAAQ;AACxB,cAAM,IAAI,kBAAkB,SAAI,QAAJ,SAAA,SAAA,SAAA,KAAM,SAAS,SAAS,QAAQ,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM,MAAM,IAAI;MAC/E;AAED,YAAM,iBAAiB,SAAS,QAAQ,IAAI,oBAAoB;AAEhE,UAAI,OAAO,WAAW,eAAe,OAAO,gBAAgB,gBAAgB;AACxE,eAAO,QAAQ,KAAK,+HAA+H;AACnJ,eAAO,aAAa,QAAQ,kBAAkB,cAAc;MAC/D;AAED,aAAO;;EACV;EAED,OAAO,QAAQ,MAAe,SAAS,IAAE;AACrC,QAAI,SAAkB,CAAA;AAEtB,eAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,IAAI,GAAG;AAC7C,UAAI,WAAW,SAAS,SAAS,MAAM,MAAK,MAAM;AAClD,UAAI,MAAM,QAAQ,KAAK,GAAG;AACtB,iBAAc,OAAA,OAAA,OAAA,OAAA,CAAA,GAAA,MAAM,GAAK,QAAO,QAAQ,OAAO,QAAQ,CAAC;MAC3D,OAAM;AACH,eAAO,QAAQ,IAAI;MACtB;IACJ;AAED,WAAO;;;AArcJ,OAAA,aAAa,OAAO,OAAO;ICtSzB,gBAAA,SAAO;EAQhB,YAAY,QAAc;AACtB,SAAK,SAAS;;EAGlB,OAAO,QAAQ,MAAe,SAAS,IAAE;AACrC,QAAI,SAAkB,CAAA;AAEtB,eAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,IAAI,GAAG;AAC7C,UAAI,WAAW,SAAS,SAAS,MAAM,MAAK,MAAM;AAClD,UAAI,MAAM,QAAQ,KAAK,GAAG;AACtB,iBAAc,OAAA,OAAA,OAAA,OAAA,CAAA,GAAA,MAAM,GAAK,SAAQ,QAAQ,OAAO,QAAQ,CAAC;MAC5D,OAAM;AACH,eAAO,QAAQ,IAAI;MACtB;IACJ;AAED,WAAO;;;AApBJ,QAAU,aAAG,IAAE,OAAK;ICAlB,gBAAO;EAGhB,YAAY,QAAc;AACtB,SAAK,SAAS;;;;;;;;;;EAWZ,MAAG;;AACL,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AACzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,OACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;;;;;EAaK,OAA+C,QAAgB,OAAe,UAAkB,MAAa;;AAC/G,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AACD,UAAI,OAAO,UAAU,aAAa;AAC9B,cAAM,IAAI,kBAAkB,qCAAqC;MACpE;AACD,UAAI,OAAO,aAAa,aAAa;AACjC,cAAM,IAAI,kBAAkB,wCAAwC;MACvE;AACD,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AACzB,UAAI,OAAO,WAAW,aAAa;AAC/B,gBAAQ,QAAQ,IAAI;MACvB;AACD,UAAI,OAAO,UAAU,aAAa;AAC9B,gBAAQ,OAAO,IAAI;MACtB;AACD,UAAI,OAAO,aAAa,aAAa;AACjC,gBAAQ,UAAU,IAAI;MACzB;AACD,UAAI,OAAO,SAAS,aAAa;AAC7B,gBAAQ,MAAM,IAAI;MACrB;AACD,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,QACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;;;;;EAaK,YAAoD,OAAe,UAAgB;;AACrF,UAAI,OAAO,UAAU,aAAa;AAC9B,cAAM,IAAI,kBAAkB,qCAAqC;MACpE;AACD,UAAI,OAAO,aAAa,aAAa;AACjC,cAAM,IAAI,kBAAkB,wCAAwC;MACvE;AACD,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AACzB,UAAI,OAAO,UAAU,aAAa;AAC9B,gBAAQ,OAAO,IAAI;MACtB;AACD,UAAI,OAAO,aAAa,aAAa;AACjC,gBAAQ,UAAU,IAAI;MACzB;AACD,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,SACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;;EAUK,eAAe,SAAkB;;AACnC,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AACzB,UAAI,OAAO,YAAY,aAAa;AAChC,gBAAQ,SAAS,IAAI;MACxB;AACD,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,OACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;;EAUK,eAAe,YAAkB;;AACnC,UAAI,OAAO,eAAe,aAAa;AACnC,cAAM,IAAI,kBAAkB,0CAA0C;MACzE;AACD,YAAM,UAAU,mCAAmC,QAAQ,gBAAgB,UAAU;AACrF,YAAM,UAAmB,CAAA;AACzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,UACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;EASK,YAAS;;AACX,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AACzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,QACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;;EAUK,SAAS,SAAkB;;AAC7B,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AACzB,UAAI,OAAO,YAAY,aAAa;AAChC,gBAAQ,SAAS,IAAI;MACxB;AACD,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,OACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;;EAUK,UAAkD,KAAY;;AAChE,UAAI,OAAO,QAAQ,aAAa;AAC5B,cAAM,IAAI,kBAAkB,mCAAmC;MAClE;AACD,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AACzB,UAAI,OAAO,QAAQ,aAAa;AAC5B,gBAAQ,KAAK,IAAI;MACpB;AACD,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,SACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;;EAUK,uBAAuB,MAAuB;;AAChD,UAAI,OAAO,SAAS,aAAa;AAC7B,cAAM,IAAI,kBAAkB,oCAAoC;MACnE;AACD,YAAM,UAAU,qCAAqC,QAAQ,UAAU,IAAI;AAC3E,YAAM,UAAmB,CAAA;AACzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,QACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;;;EAWK,uBAA+D,MAAyB,KAAW;;AACrG,UAAI,OAAO,SAAS,aAAa;AAC7B,cAAM,IAAI,kBAAkB,oCAAoC;MACnE;AACD,UAAI,OAAO,QAAQ,aAAa;AAC5B,cAAM,IAAI,kBAAkB,mCAAmC;MAClE;AACD,YAAM,UAAU,qCAAqC,QAAQ,UAAU,IAAI;AAC3E,YAAM,UAAmB,CAAA;AACzB,UAAI,OAAO,QAAQ,aAAa;AAC5B,gBAAQ,KAAK,IAAI;MACpB;AACD,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,OACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;;EAUK,uBAAuB,MAAuB;;AAChD,UAAI,OAAO,SAAS,aAAa;AAC7B,cAAM,IAAI,kBAAkB,oCAAoC;MACnE;AACD,YAAM,UAAU,qCAAqC,QAAQ,UAAU,IAAI;AAC3E,YAAM,UAAmB,CAAA;AACzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,UACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;;EAUK,mBAAmB,QAA4B;;AACjD,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AACD,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AACzB,UAAI,OAAO,WAAW,aAAa;AAC/B,gBAAQ,QAAQ,IAAI;MACvB;AACD,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,QACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;;;EAWK,mBAAmB,aAAqB,KAAW;;AACrD,UAAI,OAAO,gBAAgB,aAAa;AACpC,cAAM,IAAI,kBAAkB,2CAA2C;MAC1E;AACD,UAAI,OAAO,QAAQ,aAAa;AAC5B,cAAM,IAAI,kBAAkB,mCAAmC;MAClE;AACD,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AACzB,UAAI,OAAO,gBAAgB,aAAa;AACpC,gBAAQ,aAAa,IAAI;MAC5B;AACD,UAAI,OAAO,QAAQ,aAAa;AAC5B,gBAAQ,KAAK,IAAI;MACpB;AACD,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,OACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;EASK,iBAAc;;AAChB,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AACzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,OACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;EASK,sBAAmB;;AACrB,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AACzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,OACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;EASK,yBAAsB;;AACxB,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AACzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,QACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;EASK,yBAAsB;;AACxB,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AACzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,SACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;;EAUK,WAAmD,MAAY;;AACjE,UAAI,OAAO,SAAS,aAAa;AAC7B,cAAM,IAAI,kBAAkB,oCAAoC;MACnE;AACD,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AACzB,UAAI,OAAO,SAAS,aAAa;AAC7B,gBAAQ,MAAM,IAAI;MACrB;AACD,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,SACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;;;EAWK,eAAuD,UAAkB,aAAoB;;AAC/F,UAAI,OAAO,aAAa,aAAa;AACjC,cAAM,IAAI,kBAAkB,wCAAwC;MACvE;AACD,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AACzB,UAAI,OAAO,aAAa,aAAa;AACjC,gBAAQ,UAAU,IAAI;MACzB;AACD,UAAI,OAAO,gBAAgB,aAAa;AACpC,gBAAQ,aAAa,IAAI;MAC5B;AACD,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,SACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;;;EAWK,YAAoD,OAAe,UAAgB;;AACrF,UAAI,OAAO,UAAU,aAAa;AAC9B,cAAM,IAAI,kBAAkB,qCAAqC;MACpE;AACD,UAAI,OAAO,aAAa,aAAa;AACjC,cAAM,IAAI,kBAAkB,wCAAwC;MACvE;AACD,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AACzB,UAAI,OAAO,UAAU,aAAa;AAC9B,gBAAQ,OAAO,IAAI;MACtB;AACD,UAAI,OAAO,aAAa,aAAa;AACjC,gBAAQ,UAAU,IAAI;MACzB;AACD,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,SACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;EASK,WAAQ;;AACV,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AACzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,OACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;;EAUK,YAAoD,OAA2B;;AACjF,UAAI,OAAO,UAAU,aAAa;AAC9B,cAAM,IAAI,kBAAkB,qCAAqC;MACpE;AACD,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AACzB,UAAI,OAAO,UAAU,aAAa;AAC9B,gBAAQ,OAAO,IAAI;MACtB;AACD,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,SACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;;;EAWK,eAAe,OAAe,KAAW;;AAC3C,UAAI,OAAO,UAAU,aAAa;AAC9B,cAAM,IAAI,kBAAkB,qCAAqC;MACpE;AACD,UAAI,OAAO,QAAQ,aAAa;AAC5B,cAAM,IAAI,kBAAkB,mCAAmC;MAClE;AACD,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AACzB,UAAI,OAAO,UAAU,aAAa;AAC9B,gBAAQ,OAAO,IAAI;MACtB;AACD,UAAI,OAAO,QAAQ,aAAa;AAC5B,gBAAQ,KAAK,IAAI;MACpB;AACD,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,QACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;;;;;;EAcK,eAAe,QAAgB,QAAgB,UAAgB;;AACjE,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AACD,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AACD,UAAI,OAAO,aAAa,aAAa;AACjC,cAAM,IAAI,kBAAkB,wCAAwC;MACvE;AACD,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AACzB,UAAI,OAAO,WAAW,aAAa;AAC/B,gBAAQ,QAAQ,IAAI;MACvB;AACD,UAAI,OAAO,WAAW,aAAa;AAC/B,gBAAQ,QAAQ,IAAI;MACvB;AACD,UAAI,OAAO,aAAa,aAAa;AACjC,gBAAQ,UAAU,IAAI;MACzB;AACD,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,OACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;EASK,eAAY;;AACd,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AACzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,OACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;EASK,iBAAc;;AAChB,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AACzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,UACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;EASK,yBAAsB;;AACxB,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AACzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,QACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;;;;;EAaK,2BAA2B,OAAe,UAAgB;;AAC5D,UAAI,OAAO,UAAU,aAAa;AAC9B,cAAM,IAAI,kBAAkB,qCAAqC;MACpE;AACD,UAAI,OAAO,aAAa,aAAa;AACjC,cAAM,IAAI,kBAAkB,wCAAwC;MACvE;AACD,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AACzB,UAAI,OAAO,UAAU,aAAa;AAC9B,gBAAQ,OAAO,IAAI;MACtB;AACD,UAAI,OAAO,aAAa,aAAa;AACjC,gBAAQ,UAAU,IAAI;MACzB;AACD,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,QACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;;;EAWK,sBAAsB,QAAgB,QAAc;;AACtD,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AACD,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AACD,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AACzB,UAAI,OAAO,WAAW,aAAa;AAC/B,gBAAQ,QAAQ,IAAI;MACvB;AACD,UAAI,OAAO,WAAW,aAAa;AAC/B,gBAAQ,QAAQ,IAAI;MACvB;AACD,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,OACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;;;;;;;;;;EAkBK,oBAAoB,UAAyB,SAAkB,SAAkB,QAAiB;;AACpG,UAAI,OAAO,aAAa,aAAa;AACjC,cAAM,IAAI,kBAAkB,wCAAwC;MACvE;AACD,YAAM,UAAU,sCAAsC,QAAQ,cAAc,QAAQ;AACpF,YAAM,UAAmB,CAAA;AACzB,UAAI,OAAO,YAAY,aAAa;AAChC,gBAAQ,SAAS,IAAI;MACxB;AACD,UAAI,OAAO,YAAY,aAAa;AAChC,gBAAQ,SAAS,IAAI;MACxB;AACD,UAAI,OAAO,WAAW,aAAa;AAC/B,gBAAQ,QAAQ,IAAI;MACvB;AACD,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAMzD,cAAQ,SAAS,IAAI,KAAK,OAAO,OAAO;AACxC,iBAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,QAAQ,QAAQ,OAAO,CAAC,GAAG;AACjE,YAAI,aAAa,OAAO,KAAK,KAAK;MACrC;AAED,UAAI,OAAO,WAAW,gBAAe,WAAA,QAAA,WAAA,SAAA,SAAA,OAAQ,WAAU;AACnD,eAAO,SAAS,OAAO,IAAI,SAAQ;AACnC;MACH,OAAM;AACH,eAAO,IAAI,SAAQ;MACtB;KACJ;EAAA;;;;;;;;;;;EAWK,mBAAmB,QAAgB,QAAc;;AACnD,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AACD,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AACD,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AACzB,UAAI,OAAO,WAAW,aAAa;AAC/B,gBAAQ,QAAQ,IAAI;MACvB;AACD,UAAI,OAAO,WAAW,aAAa;AAC/B,gBAAQ,QAAQ,IAAI;MACvB;AACD,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,OACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;;;EAWK,cAAc,QAAgB,QAAc;;AAC9C,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AACD,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AACD,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AACzB,UAAI,OAAO,WAAW,aAAa;AAC/B,gBAAQ,QAAQ,IAAI;MACvB;AACD,UAAI,OAAO,WAAW,aAAa;AAC/B,gBAAQ,QAAQ,IAAI;MACvB;AACD,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,QACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;;EAUK,WAAW,WAAiB;;AAC9B,UAAI,OAAO,cAAc,aAAa;AAClC,cAAM,IAAI,kBAAkB,yCAAyC;MACxE;AACD,YAAM,UAAU,gCAAgC,QAAQ,eAAe,SAAS;AAChF,YAAM,UAAmB,CAAA;AACzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,OACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;;EAUK,cAAc,WAAiB;;AACjC,UAAI,OAAO,cAAc,aAAa;AAClC,cAAM,IAAI,kBAAkB,yCAAyC;MACxE;AACD,YAAM,UAAU,gCAAgC,QAAQ,eAAe,SAAS;AAChF,YAAM,UAAmB,CAAA;AACzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,SACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;;EAUK,cAAc,WAAiB;;AACjC,UAAI,OAAO,cAAc,aAAa;AAClC,cAAM,IAAI,kBAAkB,yCAAyC;MACxE;AACD,YAAM,UAAU,gCAAgC,QAAQ,eAAe,SAAS;AAChF,YAAM,UAAmB,CAAA;AACzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,UACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;EASK,eAAY;;AACd,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AACzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,SACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;;;;EAYK,iBAAiB,UAAkB,YAAoB,YAAmB;;AAC5E,UAAI,OAAO,aAAa,aAAa;AACjC,cAAM,IAAI,kBAAkB,wCAAwC;MACvE;AACD,UAAI,OAAO,eAAe,aAAa;AACnC,cAAM,IAAI,kBAAkB,0CAA0C;MACzE;AACD,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AACzB,UAAI,OAAO,aAAa,aAAa;AACjC,gBAAQ,UAAU,IAAI;MACzB;AACD,UAAI,OAAO,eAAe,aAAa;AACnC,gBAAQ,YAAY,IAAI;MAC3B;AACD,UAAI,OAAO,eAAe,aAAa;AACnC,gBAAQ,YAAY,IAAI;MAC3B;AACD,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,QACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;;;EAWK,iBAAiB,UAAkB,YAAkB;;AACvD,UAAI,OAAO,aAAa,aAAa;AACjC,cAAM,IAAI,kBAAkB,wCAAwC;MACvE;AACD,UAAI,OAAO,eAAe,aAAa;AACnC,cAAM,IAAI,kBAAkB,0CAA0C;MACzE;AACD,YAAM,UAAU,mCAAmC,QAAQ,cAAc,QAAQ;AACjF,YAAM,UAAmB,CAAA;AACzB,UAAI,OAAO,eAAe,aAAa;AACnC,gBAAQ,YAAY,IAAI;MAC3B;AACD,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,OACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;;EAUK,iBAAiB,UAAgB;;AACnC,UAAI,OAAO,aAAa,aAAa;AACjC,cAAM,IAAI,kBAAkB,wCAAwC;MACvE;AACD,YAAM,UAAU,mCAAmC,QAAQ,cAAc,QAAQ;AACjF,YAAM,UAAmB,CAAA;AACzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,UACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;;;;;;EAcK,iBAAiB,QAAgB,OAAe,QAAgB;;AAClE,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AACD,UAAI,OAAO,UAAU,aAAa;AAC9B,cAAM,IAAI,kBAAkB,qCAAqC;MACpE;AACD,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AACzB,UAAI,OAAO,WAAW,aAAa;AAC/B,gBAAQ,QAAQ,IAAI;MACvB;AACD,UAAI,OAAO,UAAU,aAAa;AAC9B,gBAAQ,OAAO,IAAI;MACtB;AACD,UAAI,OAAO,WAAW,aAAa;AAC/B,gBAAQ,QAAQ,IAAI;MACvB;AACD,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,QACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;;;;;;;;EAgBK,oBAAoB,QAAgB,OAAe,KAAc,QAAgB;;AACnF,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AACD,UAAI,OAAO,UAAU,aAAa;AAC9B,cAAM,IAAI,kBAAkB,qCAAqC;MACpE;AACD,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AACzB,UAAI,OAAO,WAAW,aAAa;AAC/B,gBAAQ,QAAQ,IAAI;MACvB;AACD,UAAI,OAAO,UAAU,aAAa;AAC9B,gBAAQ,OAAO,IAAI;MACtB;AACD,UAAI,OAAO,QAAQ,aAAa;AAC5B,gBAAQ,KAAK,IAAI;MACpB;AACD,UAAI,OAAO,WAAW,aAAa;AAC/B,gBAAQ,QAAQ,IAAI;MACvB;AACD,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,QACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;;;;;;;;;EAiBK,kBAAkB,UAAyB,SAAkB,SAAkB,QAAiB;;AAClG,UAAI,OAAO,aAAa,aAAa;AACjC,cAAM,IAAI,kBAAkB,wCAAwC;MACvE;AACD,YAAM,UAAU,oCAAoC,QAAQ,cAAc,QAAQ;AAClF,YAAM,UAAmB,CAAA;AACzB,UAAI,OAAO,YAAY,aAAa;AAChC,gBAAQ,SAAS,IAAI;MACxB;AACD,UAAI,OAAO,YAAY,aAAa;AAChC,gBAAQ,SAAS,IAAI;MACxB;AACD,UAAI,OAAO,WAAW,aAAa;AAC/B,gBAAQ,QAAQ,IAAI;MACvB;AACD,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAMzD,cAAQ,SAAS,IAAI,KAAK,OAAO,OAAO;AACxC,iBAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,QAAQ,QAAQ,OAAO,CAAC,GAAG;AACjE,YAAI,aAAa,OAAO,KAAK,KAAK;MACrC;AAED,UAAI,OAAO,WAAW,gBAAe,WAAA,QAAA,WAAA,SAAA,SAAA,OAAQ,WAAU;AACnD,eAAO,SAAS,OAAO,IAAI,SAAQ;AACnC;MACH,OAAM;AACH,eAAO,IAAI,SAAQ;MACtB;KACJ;EAAA;;;;;;;;;;;;;EAaK,iBAAiB,QAAgB,OAAa;;AAChD,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AACD,UAAI,OAAO,UAAU,aAAa;AAC9B,cAAM,IAAI,kBAAkB,qCAAqC;MACpE;AACD,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AACzB,UAAI,OAAO,WAAW,aAAa;AAC/B,gBAAQ,QAAQ,IAAI;MACvB;AACD,UAAI,OAAO,UAAU,aAAa;AAC9B,gBAAQ,OAAO,IAAI;MACtB;AACD,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,QACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;;;;;EAaK,mBAAmB,KAAW;;AAChC,UAAI,OAAO,QAAQ,aAAa;AAC5B,cAAM,IAAI,kBAAkB,mCAAmC;MAClE;AACD,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AACzB,UAAI,OAAO,QAAQ,aAAa;AAC5B,gBAAQ,KAAK,IAAI;MACpB;AACD,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,QACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;;;EAWK,mBAAmB,QAAgB,QAAc;;AACnD,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AACD,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AACD,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AACzB,UAAI,OAAO,WAAW,aAAa;AAC/B,gBAAQ,QAAQ,IAAI;MACvB;AACD,UAAI,OAAO,WAAW,aAAa;AAC/B,gBAAQ,QAAQ,IAAI;MACvB;AACD,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,OACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;EASK,0BAAuB;;AACzB,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AACzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,QACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;;;EAWK,wBAAwB,QAAgB,QAAc;;AACxD,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AACD,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AACD,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AACzB,UAAI,OAAO,WAAW,aAAa;AAC/B,gBAAQ,QAAQ,IAAI;MACvB;AACD,UAAI,OAAO,WAAW,aAAa;AAC/B,gBAAQ,QAAQ,IAAI;MACvB;AACD,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,OACA,KACA,YACA,OAAO;KAEd;EAAA;AACJ;ICzkDY,gBAAO;EAGhB,YAAY,QAAc;AACtB,SAAK,SAAS;;;;;;;;;;;;;;;;EAiBlB,WAAW,MAAe,OAAgB,QAAiB,SAAgB;AACvE,QAAI,OAAO,SAAS,aAAa;AAC7B,YAAM,IAAI,kBAAkB,oCAAoC;IACnE;AACD,UAAM,UAAU,2BAA2B,QAAQ,UAAU,IAAI;AACjE,UAAM,UAAmB,CAAA;AACzB,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;IACtB;AACD,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;IACvB;AACD,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;IACxB;AACD,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAMzD,YAAQ,SAAS,IAAI,KAAK,OAAO,OAAO;AACxC,eAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,QAAQ,QAAQ,OAAO,CAAC,GAAG;AACjE,UAAI,aAAa,OAAO,KAAK,KAAK;IACrC;AAED,YAAQ,SAAS,IAAI,KAAK,OAAO,OAAO;AAExC,WAAO,IAAI,SAAQ;;;;;;;;;;;;;;;;;EAiBvB,cAAc,MAAkB,OAAgB,QAAiB,SAAgB;AAC7E,QAAI,OAAO,SAAS,aAAa;AAC7B,YAAM,IAAI,kBAAkB,oCAAoC;IACnE;AACD,UAAM,UAAU,+BAA+B,QAAQ,UAAU,IAAI;AACrE,UAAM,UAAmB,CAAA;AACzB,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;IACtB;AACD,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;IACvB;AACD,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;IACxB;AACD,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAMzD,YAAQ,SAAS,IAAI,KAAK,OAAO,OAAO;AACxC,eAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,QAAQ,QAAQ,OAAO,CAAC,GAAG;AACjE,UAAI,aAAa,OAAO,KAAK,KAAK;IACrC;AAED,YAAQ,SAAS,IAAI,KAAK,OAAO,OAAO;AAExC,WAAO,IAAI,SAAQ;;;;;;;;;;;;;EAavB,WAAW,KAAW;AAClB,QAAI,OAAO,QAAQ,aAAa;AAC5B,YAAM,IAAI,kBAAkB,mCAAmC;IAClE;AACD,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAA;AACzB,QAAI,OAAO,QAAQ,aAAa;AAC5B,cAAQ,KAAK,IAAI;IACpB;AACD,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAMzD,YAAQ,SAAS,IAAI,KAAK,OAAO,OAAO;AACxC,eAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,QAAQ,QAAQ,OAAO,CAAC,GAAG;AACjE,UAAI,aAAa,OAAO,KAAK,KAAK;IACrC;AAED,YAAQ,SAAS,IAAI,KAAK,OAAO,OAAO;AAExC,WAAO,IAAI,SAAQ;;;;;;;;;;;;;;;;;EAiBvB,QAAQ,MAAY,OAAgB,QAAiB,SAAgB;AACjE,QAAI,OAAO,SAAS,aAAa;AAC7B,YAAM,IAAI,kBAAkB,oCAAoC;IACnE;AACD,UAAM,UAAU,wBAAwB,QAAQ,UAAU,IAAI;AAC9D,UAAM,UAAmB,CAAA;AACzB,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;IACtB;AACD,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;IACvB;AACD,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;IACxB;AACD,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAMzD,YAAQ,SAAS,IAAI,KAAK,OAAO,OAAO;AACxC,eAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,QAAQ,QAAQ,OAAO,CAAC,GAAG;AACjE,UAAI,aAAa,OAAO,KAAK,KAAK;IACrC;AAED,YAAQ,SAAS,IAAI,KAAK,OAAO,OAAO;AAExC,WAAO,IAAI,SAAQ;;;;;;;;;;;;;;;;;EAiBvB,SAAS,KAAa,OAAgB,QAAe;AACjD,QAAI,OAAO,QAAQ,aAAa;AAC5B,YAAM,IAAI,kBAAkB,mCAAmC;IAClE;AACD,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAA;AACzB,QAAI,OAAO,QAAQ,aAAa;AAC5B,cAAQ,KAAK,IAAI;IACpB;AACD,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;IACtB;AACD,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;IACvB;AACD,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAMzD,YAAQ,SAAS,IAAI,KAAK,OAAO,OAAO;AACxC,eAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,QAAQ,QAAQ,OAAO,CAAC,GAAG;AACjE,UAAI,aAAa,OAAO,KAAK,KAAK;IACrC;AAED,YAAQ,SAAS,IAAI,KAAK,OAAO,OAAO;AAExC,WAAO,IAAI,SAAQ;;;;;;;;;;;;;;;;;;;EAmBvB,YAAY,MAAe,OAAgB,QAAiB,YAAmB;AAC3E,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAA;AACzB,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;IACrB;AACD,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;IACtB;AACD,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;IACvB;AACD,QAAI,OAAO,eAAe,aAAa;AACnC,cAAQ,YAAY,IAAI;IAC3B;AACD,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAMzD,YAAQ,SAAS,IAAI,KAAK,OAAO,OAAO;AACxC,eAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,QAAQ,QAAQ,OAAO,CAAC,GAAG;AACjE,UAAI,aAAa,OAAO,KAAK,KAAK;IACrC;AAED,YAAQ,SAAS,IAAI,KAAK,OAAO,OAAO;AAExC,WAAO,IAAI,SAAQ;;;;;;;;;;;;;;;EAevB,MAAM,MAAc,MAAe,QAAiB,UAAkB;AAClE,QAAI,OAAO,SAAS,aAAa;AAC7B,YAAM,IAAI,kBAAkB,oCAAoC;IACnE;AACD,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAA;AACzB,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;IACrB;AACD,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;IACrB;AACD,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;IACvB;AACD,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,UAAU,IAAI;IACzB;AACD,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAMzD,YAAQ,SAAS,IAAI,KAAK,OAAO,OAAO;AACxC,eAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,QAAQ,QAAQ,OAAO,CAAC,GAAG;AACjE,UAAI,aAAa,OAAO,KAAK,KAAK;IACrC;AAED,YAAQ,SAAS,IAAI,KAAK,OAAO,OAAO;AAExC,WAAO,IAAI,SAAQ;;AAE1B;IC7TY,kBAAS;EAGlB,YAAY,QAAc;AACtB,SAAK,SAAS;;;;;;;;;;;;;EAcZ,cAAgD,YAAoB,cAAsB,SAAkB;;AAC9G,UAAI,OAAO,eAAe,aAAa;AACnC,cAAM,IAAI,kBAAkB,0CAA0C;MACzE;AACD,UAAI,OAAO,iBAAiB,aAAa;AACrC,cAAM,IAAI,kBAAkB,4CAA4C;MAC3E;AACD,YAAM,UAAU,+DAA+D,QAAQ,gBAAgB,UAAU,EAAE,QAAQ,kBAAkB,YAAY;AACzJ,YAAM,UAAmB,CAAA;AACzB,UAAI,OAAO,YAAY,aAAa;AAChC,gBAAQ,SAAS,IAAI;MACxB;AACD,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,OACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;;;;;;EAcK,eAAiD,YAAoB,cAAsB,YAAoB,MAA6C,aAAsB;;AACpL,UAAI,OAAO,eAAe,aAAa;AACnC,cAAM,IAAI,kBAAkB,0CAA0C;MACzE;AACD,UAAI,OAAO,iBAAiB,aAAa;AACrC,cAAM,IAAI,kBAAkB,4CAA4C;MAC3E;AACD,UAAI,OAAO,eAAe,aAAa;AACnC,cAAM,IAAI,kBAAkB,0CAA0C;MACzE;AACD,UAAI,OAAO,SAAS,aAAa;AAC7B,cAAM,IAAI,kBAAkB,oCAAoC;MACnE;AACD,YAAM,UAAU,+DAA+D,QAAQ,gBAAgB,UAAU,EAAE,QAAQ,kBAAkB,YAAY;AACzJ,YAAM,UAAmB,CAAA;AACzB,UAAI,OAAO,eAAe,aAAa;AACnC,gBAAQ,YAAY,IAAI;MAC3B;AACD,UAAI,OAAO,SAAS,aAAa;AAC7B,gBAAQ,MAAM,IAAI;MACrB;AACD,UAAI,OAAO,gBAAgB,aAAa;AACpC,gBAAQ,aAAa,IAAI;MAC5B;AACD,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,QACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;;;;;EAaK,YAA8C,YAAoB,cAAsB,YAAoB,SAAkB;;AAChI,UAAI,OAAO,eAAe,aAAa;AACnC,cAAM,IAAI,kBAAkB,0CAA0C;MACzE;AACD,UAAI,OAAO,iBAAiB,aAAa;AACrC,cAAM,IAAI,kBAAkB,4CAA4C;MAC3E;AACD,UAAI,OAAO,eAAe,aAAa;AACnC,cAAM,IAAI,kBAAkB,0CAA0C;MACzE;AACD,YAAM,UAAU,4EAA4E,QAAQ,gBAAgB,UAAU,EAAE,QAAQ,kBAAkB,YAAY,EAAE,QAAQ,gBAAgB,UAAU;AAC1M,YAAM,UAAmB,CAAA;AACzB,UAAI,OAAO,YAAY,aAAa;AAChC,gBAAQ,SAAS,IAAI;MACxB;AACD,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,OACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;;;;;;EAcK,eAAiD,YAAoB,cAAsB,YAAoB,MAAuD,aAAsB;;AAC9L,UAAI,OAAO,eAAe,aAAa;AACnC,cAAM,IAAI,kBAAkB,0CAA0C;MACzE;AACD,UAAI,OAAO,iBAAiB,aAAa;AACrC,cAAM,IAAI,kBAAkB,4CAA4C;MAC3E;AACD,UAAI,OAAO,eAAe,aAAa;AACnC,cAAM,IAAI,kBAAkB,0CAA0C;MACzE;AACD,YAAM,UAAU,4EAA4E,QAAQ,gBAAgB,UAAU,EAAE,QAAQ,kBAAkB,YAAY,EAAE,QAAQ,gBAAgB,UAAU;AAC1M,YAAM,UAAmB,CAAA;AACzB,UAAI,OAAO,SAAS,aAAa;AAC7B,gBAAQ,MAAM,IAAI;MACrB;AACD,UAAI,OAAO,gBAAgB,aAAa;AACpC,gBAAQ,aAAa,IAAI;MAC5B;AACD,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,SACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;;;;EAYK,eAAe,YAAoB,cAAsB,YAAkB;;AAC7E,UAAI,OAAO,eAAe,aAAa;AACnC,cAAM,IAAI,kBAAkB,0CAA0C;MACzE;AACD,UAAI,OAAO,iBAAiB,aAAa;AACrC,cAAM,IAAI,kBAAkB,4CAA4C;MAC3E;AACD,UAAI,OAAO,eAAe,aAAa;AACnC,cAAM,IAAI,kBAAkB,0CAA0C;MACzE;AACD,YAAM,UAAU,4EAA4E,QAAQ,gBAAgB,UAAU,EAAE,QAAQ,kBAAkB,YAAY,EAAE,QAAQ,gBAAgB,UAAU;AAC1M,YAAM,UAAmB,CAAA;AACzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,UACA,KACA,YACA,OAAO;KAEd;EAAA;AACJ;ICzNY,kBAAS;EAGlB,YAAY,QAAc;AACtB,SAAK,SAAS;;;;;;;;;;;;;EAcZ,eAAe,YAAoB,SAAoB,QAAe;;AACxE,UAAI,OAAO,eAAe,aAAa;AACnC,cAAM,IAAI,kBAAkB,0CAA0C;MACzE;AACD,YAAM,UAAU,qCAAqC,QAAQ,gBAAgB,UAAU;AACvF,YAAM,UAAmB,CAAA;AACzB,UAAI,OAAO,YAAY,aAAa;AAChC,gBAAQ,SAAS,IAAI;MACxB;AACD,UAAI,OAAO,WAAW,aAAa;AAC/B,gBAAQ,QAAQ,IAAI;MACvB;AACD,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,OACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;;;;;;;;EAgBK,gBAAgB,YAAoB,MAAe,OAAiB,OAAgB,QAA0B,SAAkB,aAAoB;;AACtJ,UAAI,OAAO,eAAe,aAAa;AACnC,cAAM,IAAI,kBAAkB,0CAA0C;MACzE;AACD,YAAM,UAAU,qCAAqC,QAAQ,gBAAgB,UAAU;AACvF,YAAM,UAAmB,CAAA;AACzB,UAAI,OAAO,SAAS,aAAa;AAC7B,gBAAQ,MAAM,IAAI;MACrB;AACD,UAAI,OAAO,UAAU,aAAa;AAC9B,gBAAQ,OAAO,IAAI;MACtB;AACD,UAAI,OAAO,UAAU,aAAa;AAC9B,gBAAQ,MAAM,IAAI;MACrB;AACD,UAAI,OAAO,WAAW,aAAa;AAC/B,gBAAQ,QAAQ,IAAI;MACvB;AACD,UAAI,OAAO,YAAY,aAAa;AAChC,gBAAQ,SAAS,IAAI;MACxB;AACD,UAAI,OAAO,gBAAgB,aAAa;AACpC,gBAAQ,aAAa,IAAI;MAC5B;AACD,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,QACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;;;EAWK,aAAa,YAAoB,aAAmB;;AACtD,UAAI,OAAO,eAAe,aAAa;AACnC,cAAM,IAAI,kBAAkB,0CAA0C;MACzE;AACD,UAAI,OAAO,gBAAgB,aAAa;AACpC,cAAM,IAAI,kBAAkB,2CAA2C;MAC1E;AACD,YAAM,UAAU,mDAAmD,QAAQ,gBAAgB,UAAU,EAAE,QAAQ,iBAAiB,WAAW;AAC3I,YAAM,UAAmB,CAAA;AACzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,OACA,KACA,YACA,OAAO;KAEd;EAAA;AACJ;ICnIY,gBAAO;EAGhB,YAAY,QAAc;AACtB,SAAK,SAAS;;;;;;;;;;;EAYZ,MAAM,OAAa;;AACrB,UAAI,OAAO,UAAU,aAAa;AAC9B,cAAM,IAAI,kBAAkB,qCAAqC;MACpE;AACD,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AACzB,UAAI,OAAO,UAAU,aAAa;AAC9B,gBAAQ,OAAO,IAAI;MACtB;AACD,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,iBAAiB;QACjB,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,QACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;;EAUK,SAAS,OAAa;;AACxB,UAAI,OAAO,UAAU,aAAa;AAC9B,cAAM,IAAI,kBAAkB,qCAAqC;MACpE;AACD,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AACzB,UAAI,OAAO,UAAU,aAAa;AAC9B,gBAAQ,OAAO,IAAI;MACtB;AACD,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,iBAAiB;QACjB,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,QACA,KACA,YACA,OAAO;KAEd;EAAA;AACJ;ICzEY,eAAM;EAGf,YAAY,QAAc;AACtB,SAAK,SAAS;;;;;;;;;;;;EAaZ,MAAG;;AACL,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AACzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,OACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;EASK,YAAS;;AACX,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AACzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,OACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;EASK,iBAAc;;AAChB,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AACzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,OACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;EASK,gBAAa;;AACf,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AACzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,OACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;EASK,kBAAe;;AACjB,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AACzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,OACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;EASK,sBAAmB;;AACrB,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AACzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,OACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;EASK,iBAAc;;AAChB,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AACzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,OACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;EASK,gBAAa;;AACf,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AACzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,OACA,KACA,YACA,OAAO;KAEd;EAAA;AACJ;ICjNY,kBAAS;EAGlB,YAAY,QAAc;AACtB,SAAK,SAAS;;;;;;;;;;;;;EAcZ,iBAAiB,SAAiB,cAAsB,UAAgB;;AAC1E,UAAI,OAAO,YAAY,aAAa;AAChC,cAAM,IAAI,kBAAkB,uCAAuC;MACtE;AACD,UAAI,OAAO,iBAAiB,aAAa;AACrC,cAAM,IAAI,kBAAkB,4CAA4C;MAC3E;AACD,UAAI,OAAO,aAAa,aAAa;AACjC,cAAM,IAAI,kBAAkB,wCAAwC;MACvE;AACD,YAAM,UAAU,0CAA0C,QAAQ,aAAa,OAAO;AACtF,YAAM,UAAmB,CAAA;AACzB,UAAI,OAAO,iBAAiB,aAAa;AACrC,gBAAQ,cAAc,IAAI;MAC7B;AACD,UAAI,OAAO,aAAa,aAAa;AACjC,gBAAQ,UAAU,IAAI;MACzB;AACD,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,QACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;;;EAWK,iBAAiB,SAAiB,cAAoB;;AACxD,UAAI,OAAO,YAAY,aAAa;AAChC,cAAM,IAAI,kBAAkB,uCAAuC;MACtE;AACD,UAAI,OAAO,iBAAiB,aAAa;AACrC,cAAM,IAAI,kBAAkB,4CAA4C;MAC3E;AACD,YAAM,UAAU,yDAAyD,QAAQ,aAAa,OAAO,EAAE,QAAQ,kBAAkB,YAAY;AAC7I,YAAM,UAAmB,CAAA;AACzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,UACA,KACA,YACA,OAAO;KAEd;EAAA;AACJ;ICjFY,gBAAO;EAGhB,YAAY,QAAc;AACtB,SAAK,SAAS;;;;;;;;;;;;;EAcZ,UAAU,UAAkB,SAAoB,QAAe;;AACjE,UAAI,OAAO,aAAa,aAAa;AACjC,cAAM,IAAI,kBAAkB,wCAAwC;MACvE;AACD,YAAM,UAAU,oCAAoC,QAAQ,cAAc,QAAQ;AAClF,YAAM,UAAmB,CAAA;AACzB,UAAI,OAAO,YAAY,aAAa;AAChC,gBAAQ,SAAS,IAAI;MACxB;AACD,UAAI,OAAO,WAAW,aAAa;AAC/B,gBAAQ,QAAQ,IAAI;MACvB;AACD,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,OACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;;;;;;;;;;;;EAoBK,WAAW,UAAkB,QAAgB,MAAY,aAAwB,aAAa,CAAC,aAAwB;EAAA,GAAO;;AAChI,UAAI,OAAO,aAAa,aAAa;AACjC,cAAM,IAAI,kBAAkB,wCAAwC;MACvE;AACD,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AACD,UAAI,OAAO,SAAS,aAAa;AAC7B,cAAM,IAAI,kBAAkB,oCAAoC;MACnE;AACD,YAAM,UAAU,oCAAoC,QAAQ,cAAc,QAAQ;AAClF,YAAM,UAAmB,CAAA;AACzB,UAAI,OAAO,WAAW,aAAa;AAC/B,gBAAQ,QAAQ,IAAI;MACvB;AACD,UAAI,OAAO,SAAS,aAAa;AAC7B,gBAAQ,MAAM,IAAI;MACrB;AACD,UAAI,OAAO,gBAAgB,aAAa;AACpC,gBAAQ,aAAa,IAAI;MAC5B;AACD,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,cACrB,QACA,KACA,YACA,SACA,UAAU;KAEjB;EAAA;;;;;;;;;;;EAWK,QAAQ,UAAkB,QAAc;;AAC1C,UAAI,OAAO,aAAa,aAAa;AACjC,cAAM,IAAI,kBAAkB,wCAAwC;MACvE;AACD,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AACD,YAAM,UAAU,6CAA6C,QAAQ,cAAc,QAAQ,EAAE,QAAQ,YAAY,MAAM;AACvH,YAAM,UAAmB,CAAA;AACzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,OACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;;;;;EAaK,WAAW,UAAkB,QAAgB,MAAe,aAAsB;;AACpF,UAAI,OAAO,aAAa,aAAa;AACjC,cAAM,IAAI,kBAAkB,wCAAwC;MACvE;AACD,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AACD,YAAM,UAAU,6CAA6C,QAAQ,cAAc,QAAQ,EAAE,QAAQ,YAAY,MAAM;AACvH,YAAM,UAAmB,CAAA;AACzB,UAAI,OAAO,SAAS,aAAa;AAC7B,gBAAQ,MAAM,IAAI;MACrB;AACD,UAAI,OAAO,gBAAgB,aAAa;AACpC,gBAAQ,aAAa,IAAI;MAC5B;AACD,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,OACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;;;EAWK,WAAW,UAAkB,QAAc;;AAC7C,UAAI,OAAO,aAAa,aAAa;AACjC,cAAM,IAAI,kBAAkB,wCAAwC;MACvE;AACD,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AACD,YAAM,UAAU,6CAA6C,QAAQ,cAAc,QAAQ,EAAE,QAAQ,YAAY,MAAM;AACvH,YAAM,UAAmB,CAAA;AACzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,UACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;;;EAWD,gBAAgB,UAAkB,QAAc;AAC5C,QAAI,OAAO,aAAa,aAAa;AACjC,YAAM,IAAI,kBAAkB,wCAAwC;IACvE;AACD,QAAI,OAAO,WAAW,aAAa;AAC/B,YAAM,IAAI,kBAAkB,sCAAsC;IACrE;AACD,UAAM,UAAU,sDAAsD,QAAQ,cAAc,QAAQ,EAAE,QAAQ,YAAY,MAAM;AAChI,UAAM,UAAmB,CAAA;AACzB,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAMzD,YAAQ,SAAS,IAAI,KAAK,OAAO,OAAO;AACxC,eAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,QAAQ,QAAQ,OAAO,CAAC,GAAG;AACjE,UAAI,aAAa,OAAO,KAAK,KAAK;IACrC;AAED,YAAQ,SAAS,IAAI,KAAK,OAAO,OAAO;AAExC,WAAO,IAAI,SAAQ;;;;;;;;;;;;;;;;;;;;;;;EAuBvB,eAAe,UAAkB,QAAgB,OAAgB,QAAiB,SAAwB,SAAkB,aAAsB,aAAsB,cAAuB,SAAkB,UAAmB,YAAqB,QAAoB;AACzQ,QAAI,OAAO,aAAa,aAAa;AACjC,YAAM,IAAI,kBAAkB,wCAAwC;IACvE;AACD,QAAI,OAAO,WAAW,aAAa;AAC/B,YAAM,IAAI,kBAAkB,sCAAsC;IACrE;AACD,UAAM,UAAU,qDAAqD,QAAQ,cAAc,QAAQ,EAAE,QAAQ,YAAY,MAAM;AAC/H,UAAM,UAAmB,CAAA;AACzB,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;IACtB;AACD,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;IACvB;AACD,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;IACxB;AACD,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;IACxB;AACD,QAAI,OAAO,gBAAgB,aAAa;AACpC,cAAQ,aAAa,IAAI;IAC5B;AACD,QAAI,OAAO,gBAAgB,aAAa;AACpC,cAAQ,aAAa,IAAI;IAC5B;AACD,QAAI,OAAO,iBAAiB,aAAa;AACrC,cAAQ,cAAc,IAAI;IAC7B;AACD,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;IACxB;AACD,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,UAAU,IAAI;IACzB;AACD,QAAI,OAAO,eAAe,aAAa;AACnC,cAAQ,YAAY,IAAI;IAC3B;AACD,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;IACvB;AACD,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAMzD,YAAQ,SAAS,IAAI,KAAK,OAAO,OAAO;AACxC,eAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,QAAQ,QAAQ,OAAO,CAAC,GAAG;AACjE,UAAI,aAAa,OAAO,KAAK,KAAK;IACrC;AAED,YAAQ,SAAS,IAAI,KAAK,OAAO,OAAO;AAExC,WAAO,IAAI,SAAQ;;;;;;;;;;;;EAYvB,YAAY,UAAkB,QAAc;AACxC,QAAI,OAAO,aAAa,aAAa;AACjC,YAAM,IAAI,kBAAkB,wCAAwC;IACvE;AACD,QAAI,OAAO,WAAW,aAAa;AAC/B,YAAM,IAAI,kBAAkB,sCAAsC;IACrE;AACD,UAAM,UAAU,kDAAkD,QAAQ,cAAc,QAAQ,EAAE,QAAQ,YAAY,MAAM;AAC5H,UAAM,UAAmB,CAAA;AACzB,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAMzD,YAAQ,SAAS,IAAI,KAAK,OAAO,OAAO;AACxC,eAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,QAAQ,QAAQ,OAAO,CAAC,GAAG;AACjE,UAAI,aAAa,OAAO,KAAK,KAAK;IACrC;AAED,YAAQ,SAAS,IAAI,KAAK,OAAO,OAAO;AAExC,WAAO,IAAI,SAAQ;;AAE1B;IClWY,cAAK;EAGd,YAAY,QAAc;AACtB,SAAK,SAAS;;;;;;;;;;;;EAaZ,KAA6C,SAAoB,QAAe;;AAClF,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AACzB,UAAI,OAAO,YAAY,aAAa;AAChC,gBAAQ,SAAS,IAAI;MACxB;AACD,UAAI,OAAO,WAAW,aAAa;AAC/B,gBAAQ,QAAQ,IAAI;MACvB;AACD,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,OACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;;;;EAYK,OAA+C,QAAgB,MAAc,OAAgB;;AAC/F,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AACD,UAAI,OAAO,SAAS,aAAa;AAC7B,cAAM,IAAI,kBAAkB,oCAAoC;MACnE;AACD,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AACzB,UAAI,OAAO,WAAW,aAAa;AAC/B,gBAAQ,QAAQ,IAAI;MACvB;AACD,UAAI,OAAO,SAAS,aAAa;AAC7B,gBAAQ,MAAM,IAAI;MACrB;AACD,UAAI,OAAO,UAAU,aAAa;AAC9B,gBAAQ,OAAO,IAAI;MACtB;AACD,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,QACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;;EAUK,IAA4C,QAAc;;AAC5D,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AACD,YAAM,UAAU,kBAAkB,QAAQ,YAAY,MAAM;AAC5D,YAAM,UAAmB,CAAA;AACzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,OACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;;;EAWK,WAAmD,QAAgB,MAAY;;AACjF,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AACD,UAAI,OAAO,SAAS,aAAa;AAC7B,cAAM,IAAI,kBAAkB,oCAAoC;MACnE;AACD,YAAM,UAAU,kBAAkB,QAAQ,YAAY,MAAM;AAC5D,YAAM,UAAmB,CAAA;AACzB,UAAI,OAAO,SAAS,aAAa;AAC7B,gBAAQ,MAAM,IAAI;MACrB;AACD,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,OACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;;EAUK,OAAO,QAAc;;AACvB,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AACD,YAAM,UAAU,kBAAkB,QAAQ,YAAY,MAAM;AAC5D,YAAM,UAAmB,CAAA;AACzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,UACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;;;;EAYK,gBAAgB,QAAgB,SAAoB,QAAe;;AACrE,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AACD,YAAM,UAAU,8BAA8B,QAAQ,YAAY,MAAM;AACxE,YAAM,UAAmB,CAAA;AACzB,UAAI,OAAO,YAAY,aAAa;AAChC,gBAAQ,SAAS,IAAI;MACxB;AACD,UAAI,OAAO,WAAW,aAAa;AAC/B,gBAAQ,QAAQ,IAAI;MACvB;AACD,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,OACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;;;;;;;;;;;;;;;EAuBK,iBAAiB,QAAgB,OAAiB,OAAgB,QAAiB,OAAgB,KAAc,MAAa;;AAChI,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AACD,UAAI,OAAO,UAAU,aAAa;AAC9B,cAAM,IAAI,kBAAkB,qCAAqC;MACpE;AACD,YAAM,UAAU,8BAA8B,QAAQ,YAAY,MAAM;AACxE,YAAM,UAAmB,CAAA;AACzB,UAAI,OAAO,UAAU,aAAa;AAC9B,gBAAQ,OAAO,IAAI;MACtB;AACD,UAAI,OAAO,WAAW,aAAa;AAC/B,gBAAQ,QAAQ,IAAI;MACvB;AACD,UAAI,OAAO,UAAU,aAAa;AAC9B,gBAAQ,OAAO,IAAI;MACtB;AACD,UAAI,OAAO,UAAU,aAAa;AAC9B,gBAAQ,OAAO,IAAI;MACtB;AACD,UAAI,OAAO,QAAQ,aAAa;AAC5B,gBAAQ,KAAK,IAAI;MACpB;AACD,UAAI,OAAO,SAAS,aAAa;AAC7B,gBAAQ,MAAM,IAAI;MACrB;AACD,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,QACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;;;EAWK,cAAc,QAAgB,cAAoB;;AACpD,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AACD,UAAI,OAAO,iBAAiB,aAAa;AACrC,cAAM,IAAI,kBAAkB,4CAA4C;MAC3E;AACD,YAAM,UAAU,6CAA6C,QAAQ,YAAY,MAAM,EAAE,QAAQ,kBAAkB,YAAY;AAC/H,YAAM,UAAmB,CAAA;AACzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,OACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;;;;;EAaK,iBAAiB,QAAgB,cAAsB,OAAe;;AACxE,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AACD,UAAI,OAAO,iBAAiB,aAAa;AACrC,cAAM,IAAI,kBAAkB,4CAA4C;MAC3E;AACD,UAAI,OAAO,UAAU,aAAa;AAC9B,cAAM,IAAI,kBAAkB,qCAAqC;MACpE;AACD,YAAM,UAAU,6CAA6C,QAAQ,YAAY,MAAM,EAAE,QAAQ,kBAAkB,YAAY;AAC/H,YAAM,UAAmB,CAAA;AACzB,UAAI,OAAO,UAAU,aAAa;AAC9B,gBAAQ,OAAO,IAAI;MACtB;AACD,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,SACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;;;EAWK,iBAAiB,QAAgB,cAAoB;;AACvD,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AACD,UAAI,OAAO,iBAAiB,aAAa;AACrC,cAAM,IAAI,kBAAkB,4CAA4C;MAC3E;AACD,YAAM,UAAU,6CAA6C,QAAQ,YAAY,MAAM,EAAE,QAAQ,kBAAkB,YAAY;AAC/H,YAAM,UAAmB,CAAA;AACzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,UACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;;;;;;;;EAgBK,uBAAuB,QAAgB,cAAsB,QAAgB,QAAc;;AAC7F,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AACD,UAAI,OAAO,iBAAiB,aAAa;AACrC,cAAM,IAAI,kBAAkB,4CAA4C;MAC3E;AACD,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AACD,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AACD,YAAM,UAAU,oDAAoD,QAAQ,YAAY,MAAM,EAAE,QAAQ,kBAAkB,YAAY;AACtI,YAAM,UAAmB,CAAA;AACzB,UAAI,OAAO,WAAW,aAAa;AAC/B,gBAAQ,QAAQ,IAAI;MACvB;AACD,UAAI,OAAO,WAAW,aAAa;AAC/B,gBAAQ,QAAQ,IAAI;MACvB;AACD,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,SACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;;EAUK,SAAiD,QAAc;;AACjE,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AACD,YAAM,UAAU,wBAAwB,QAAQ,YAAY,MAAM;AAClE,YAAM,UAAmB,CAAA;AACzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,OACA,KACA,YACA,OAAO;KAEd;EAAA;;;;;;;;;;;EAWK,YAAoD,QAAgB,OAAa;;AACnF,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AACD,UAAI,OAAO,UAAU,aAAa;AAC9B,cAAM,IAAI,kBAAkB,qCAAqC;MACpE;AACD,YAAM,UAAU,wBAAwB,QAAQ,YAAY,MAAM;AAClE,YAAM,UAAmB,CAAA;AACzB,UAAI,OAAO,UAAU,aAAa;AAC9B,gBAAQ,OAAO,IAAI;MACtB;AACD,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,YAAM,aAA2C;QAC7C,gBAAgB;;AAIpB,aAAO,MAAM,KAAK,OAAO,KACrB,OACA,KACA,YACA,OAAO;KAEd;EAAA;AACJ;ICpfY,mBAAU;;AAOZ,WAAA,OAAO,CAAC,SAAwB;AACnC,SAAO,SAAS,IAAI;AACxB;AAWO,WAAA,QAAQ,CAAC,SAAwB;AACpC,SAAO,UAAU,IAAI;AACzB;AAQO,WAAA,SAAS,CAAC,SAAwB;AACrC,SAAO,WAAW,IAAI;AAC1B;AAQO,WAAA,SAAS,CAAC,SAAwB;AACrC,SAAO,WAAW,IAAI;AAC1B;AAQO,WAAA,SAAS,CAAC,SAAwB;AACrC,SAAO,WAAW,IAAI;AAC1B;ICpDS,aAAI;;;;;;;;EASN,OAAO,MAAG;AACb,WAAO;;;;;;;;;;;;EAaJ,OAAO,KAAK,IAAY,SAAiB,IAAE;AAC9C,QAAI,WAAW,IAAI;AACf,aAAO,QAAQ,EAAE;IACpB;AACD,WAAO,QAAQ,EAAE,IAAI,MAAM;;;;;;;;;;;EAYxB,OAAO,MAAM,SAAiB,IAAE;AACnC,QAAI,WAAW,IAAI;AACf,aAAO;IACV;AACD,WAAO,SAAS,MAAM;;;;;;;;;EAUnB,OAAO,SAAM;AAChB,WAAO;;;;;;;;;;;;EAaJ,OAAO,KAAK,IAAY,OAAe,IAAE;AAC5C,QAAI,SAAS,IAAI;AACb,aAAO,QAAQ,EAAE;IACpB;AACD,WAAO,QAAQ,EAAE,IAAI,IAAI;;;;;;;;;;;EAYtB,OAAO,OAAO,IAAU;AAC3B,WAAO,UAAU,EAAE;;;;;;;;EAShB,OAAO,MAAM,MAAY;AAC5B,WAAO,SAAS,IAAI;;AAE3B;;;IChGY,WAAA,IAAE;;;;;;;EAuBJ,OAAO,OAAO,IAAU;AAC3B,WAAO;;;;;;;;EASJ,OAAO,OAAO,UAAkB,GAAC;AAEpC,UAAM,SAAS,uBAAA,KAAE,IAAA,KAAA,gBAAA,EAAF,KAAA,GAAE;AACjB,QAAI,gBAAgB;AACpB,aAAS,IAAI,GAAG,IAAI,SAAS,KAAK;AAC9B,YAAM,iBAAiB,KAAK,MAAM,KAAK,OAAM,IAAK,EAAE,EAAE,SAAS,EAAE;AACjE,uBAAiB;IACpB;AACD,WAAO,SAAS;;AAEvB;;AAnCO,QAAM,MAAM,oBAAI,KAAI;AACpB,QAAM,MAAM,KAAK,MAAM,IAAI,QAAO,IAAK,GAAI;AAC3C,QAAM,OAAO,IAAI,gBAAe;AAGhC,QAAM,eAAe,IAAI,SAAS,EAAE,IAAI,KAAK,SAAS,EAAE,EAAE,SAAS,GAAG,GAAG;AACzE,SAAO;AACX;IClBQ;CAAZ,SAAYG,oBAAiB;AACzB,EAAAA,mBAAA,MAAA,IAAA;AACJ,GAFY,sBAAA,oBAEX,CAAA,EAAA;ICFW;CAAZ,SAAYC,uBAAoB;AAC5B,EAAAA,sBAAA,OAAA,IAAA;AACA,EAAAA,sBAAA,OAAA,IAAA;AACA,EAAAA,sBAAA,MAAA,IAAA;AACA,EAAAA,sBAAA,cAAA,IAAA;AACJ,GALY,yBAAA,uBAKX,CAAA,EAAA;ICLW;CAAZ,SAAYC,gBAAa;AACrB,EAAAA,eAAA,QAAA,IAAA;AACA,EAAAA,eAAA,OAAA,IAAA;AACA,EAAAA,eAAA,OAAA,IAAA;AACA,EAAAA,eAAA,WAAA,IAAA;AACA,EAAAA,eAAA,UAAA,IAAA;AACA,EAAAA,eAAA,WAAA,IAAA;AACA,EAAAA,eAAA,OAAA,IAAA;AACA,EAAAA,eAAA,KAAA,IAAA;AACA,EAAAA,eAAA,aAAA,IAAA;AACA,EAAAA,eAAA,SAAA,IAAA;AACA,EAAAA,eAAA,QAAA,IAAA;AACA,EAAAA,eAAA,SAAA,IAAA;AACA,EAAAA,eAAA,MAAA,IAAA;AACA,EAAAA,eAAA,UAAA,IAAA;AACA,EAAAA,eAAA,QAAA,IAAA;AACA,EAAAA,eAAA,QAAA,IAAA;AACA,EAAAA,eAAA,QAAA,IAAA;AACA,EAAAA,eAAA,UAAA,IAAA;AACA,EAAAA,eAAA,WAAA,IAAA;AACA,EAAAA,eAAA,QAAA,IAAA;AACA,EAAAA,eAAA,MAAA,IAAA;AACA,EAAAA,eAAA,MAAA,IAAA;AACA,EAAAA,eAAA,QAAA,IAAA;AACA,EAAAA,eAAA,eAAA,IAAA;AACA,EAAAA,eAAA,OAAA,IAAA;AACA,EAAAA,eAAA,YAAA,IAAA;AACA,EAAAA,eAAA,OAAA,IAAA;AACA,EAAAA,eAAA,SAAA,IAAA;AACA,EAAAA,eAAA,QAAA,IAAA;AACA,EAAAA,eAAA,YAAA,IAAA;AACA,EAAAA,eAAA,eAAA,IAAA;AACA,EAAAA,eAAA,QAAA,IAAA;AACA,EAAAA,eAAA,WAAA,IAAA;AACA,EAAAA,eAAA,OAAA,IAAA;AACA,EAAAA,eAAA,QAAA,IAAA;AACA,EAAAA,eAAA,QAAA,IAAA;AACA,EAAAA,eAAA,MAAA,IAAA;AACA,EAAAA,eAAA,MAAA,IAAA;AACA,EAAAA,eAAA,MAAA,IAAA;AACJ,GAxCY,kBAAA,gBAwCX,CAAA,EAAA;ICxCW;CAAZ,SAAYC,UAAO;AACf,EAAAA,SAAA,cAAA,IAAA;AACA,EAAAA,SAAA,oBAAA,IAAA;AACA,EAAAA,SAAA,cAAA,IAAA;AACA,EAAAA,SAAA,iBAAA,IAAA;AACA,EAAAA,SAAA,oBAAA,IAAA;AACA,EAAAA,SAAA,UAAA,IAAA;AACA,EAAAA,SAAA,gBAAA,IAAA;AACA,EAAAA,SAAA,QAAA,IAAA;AACA,EAAAA,SAAA,cAAA,IAAA;AACA,EAAAA,SAAA,eAAA,IAAA;AACA,EAAAA,SAAA,kBAAA,IAAA;AACA,EAAAA,SAAA,WAAA,IAAA;AACA,EAAAA,SAAA,OAAA,IAAA;AACA,EAAAA,SAAA,WAAA,IAAA;AACJ,GAfY,YAAA,UAeX,CAAA,EAAA;ICfW;CAAZ,SAAYC,aAAU;AAClB,EAAAA,YAAA,iBAAA,IAAA;AACA,EAAAA,YAAA,WAAA,IAAA;AACA,EAAAA,YAAA,OAAA,IAAA;AACA,EAAAA,YAAA,UAAA,IAAA;AACA,EAAAA,YAAA,YAAA,IAAA;AACA,EAAAA,YAAA,UAAA,IAAA;AACA,EAAAA,YAAA,KAAA,IAAA;AACA,EAAAA,YAAA,WAAA,IAAA;AACA,EAAAA,YAAA,KAAA,IAAA;AACA,EAAAA,YAAA,YAAA,IAAA;AACA,EAAAA,YAAA,SAAA,IAAA;AACA,EAAAA,YAAA,iBAAA,IAAA;AACA,EAAAA,YAAA,eAAA,IAAA;AACA,EAAAA,YAAA,MAAA,IAAA;AACA,EAAAA,YAAA,KAAA,IAAA;AACA,EAAAA,YAAA,SAAA,IAAA;AACJ,GAjBY,eAAA,aAiBX,CAAA,EAAA;ICjBW;CAAZ,SAAYC,OAAI;AACZ,EAAAA,MAAA,aAAA,IAAA;AACA,EAAAA,MAAA,QAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,oBAAA,IAAA;AACA,EAAAA,MAAA,WAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,mBAAA,IAAA;AACA,EAAAA,MAAA,WAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,YAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,OAAA,IAAA;AACA,EAAAA,MAAA,aAAA,IAAA;AACA,EAAAA,MAAA,YAAA,IAAA;AACA,EAAAA,MAAA,UAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,sBAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,QAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,QAAA,IAAA;AACA,EAAAA,MAAA,UAAA,IAAA;AACA,EAAAA,MAAA,kBAAA,IAAA;AACA,EAAAA,MAAA,QAAA,IAAA;AACA,EAAAA,MAAA,UAAA,IAAA;AACA,EAAAA,MAAA,wBAAA,IAAA;AACA,EAAAA,MAAA,QAAA,IAAA;AACA,EAAAA,MAAA,aAAA,IAAA;AACA,EAAAA,MAAA,OAAA,IAAA;AACA,EAAAA,MAAA,OAAA,IAAA;AACA,EAAAA,MAAA,aAAA,IAAA;AACA,EAAAA,MAAA,UAAA,IAAA;AACA,EAAAA,MAAA,8BAAA,IAAA;AACA,EAAAA,MAAA,oBAAA,IAAA;AACA,EAAAA,MAAA,UAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,WAAA,IAAA;AACA,EAAAA,MAAA,WAAA,IAAA;AACA,EAAAA,MAAA,MAAA,IAAA;AACA,EAAAA,MAAA,QAAA,IAAA;AACA,EAAAA,MAAA,eAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,UAAA,IAAA;AACA,EAAAA,MAAA,UAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,mBAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,OAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,OAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,UAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,MAAA,IAAA;AACA,EAAAA,MAAA,QAAA,IAAA;AACA,EAAAA,MAAA,6BAAA,IAAA;AACA,EAAAA,MAAA,OAAA,IAAA;AACA,EAAAA,MAAA,eAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,OAAA,IAAA;AACA,EAAAA,MAAA,QAAA,IAAA;AACA,EAAAA,MAAA,QAAA,IAAA;AACA,EAAAA,MAAA,cAAA,IAAA;AACA,EAAAA,MAAA,kBAAA,IAAA;AACA,EAAAA,MAAA,QAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,WAAA,IAAA;AACA,EAAAA,MAAA,QAAA,IAAA;AACA,EAAAA,MAAA,UAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,OAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,WAAA,IAAA;AACA,EAAAA,MAAA,OAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,uBAAA,IAAA;AACA,EAAAA,MAAA,MAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,QAAA,IAAA;AACA,EAAAA,MAAA,OAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,QAAA,IAAA;AACA,EAAAA,MAAA,OAAA,IAAA;AACA,EAAAA,MAAA,YAAA,IAAA;AACA,EAAAA,MAAA,OAAA,IAAA;AACA,EAAAA,MAAA,YAAA,IAAA;AACA,EAAAA,MAAA,UAAA,IAAA;AACA,EAAAA,MAAA,UAAA,IAAA;AACA,EAAAA,MAAA,oBAAA,IAAA;AACA,EAAAA,MAAA,YAAA,IAAA;AACA,EAAAA,MAAA,QAAA,IAAA;AACA,EAAAA,MAAA,8BAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,OAAA,IAAA;AACA,EAAAA,MAAA,YAAA,IAAA;AACA,EAAAA,MAAA,eAAA,IAAA;AACA,EAAAA,MAAA,UAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,WAAA,IAAA;AACA,EAAAA,MAAA,YAAA,IAAA;AACA,EAAAA,MAAA,QAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,QAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,YAAA,IAAA;AACA,EAAAA,MAAA,UAAA,IAAA;AACA,EAAAA,MAAA,QAAA,IAAA;AACA,EAAAA,MAAA,iBAAA,IAAA;AACA,EAAAA,MAAA,gBAAA,IAAA;AACA,EAAAA,MAAA,MAAA,IAAA;AACA,EAAAA,MAAA,OAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,YAAA,IAAA;AACA,EAAAA,MAAA,UAAA,IAAA;AACA,EAAAA,MAAA,YAAA,IAAA;AACA,EAAAA,MAAA,YAAA,IAAA;AACA,EAAAA,MAAA,WAAA,IAAA;AACA,EAAAA,MAAA,QAAA,IAAA;AACA,EAAAA,MAAA,UAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,OAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,WAAA,IAAA;AACA,EAAAA,MAAA,aAAA,IAAA;AACA,EAAAA,MAAA,QAAA,IAAA;AACA,EAAAA,MAAA,OAAA,IAAA;AACA,EAAAA,MAAA,OAAA,IAAA;AACA,EAAAA,MAAA,YAAA,IAAA;AACA,EAAAA,MAAA,MAAA,IAAA;AACA,EAAAA,MAAA,UAAA,IAAA;AACA,EAAAA,MAAA,QAAA,IAAA;AACA,EAAAA,MAAA,MAAA,IAAA;AACA,EAAAA,MAAA,aAAA,IAAA;AACA,EAAAA,MAAA,OAAA,IAAA;AACA,EAAAA,MAAA,gBAAA,IAAA;AACA,EAAAA,MAAA,QAAA,IAAA;AACA,EAAAA,MAAA,iBAAA,IAAA;AACA,EAAAA,MAAA,YAAA,IAAA;AACA,EAAAA,MAAA,UAAA,IAAA;AACA,EAAAA,MAAA,UAAA,IAAA;AACA,EAAAA,MAAA,OAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,QAAA,IAAA;AACA,EAAAA,MAAA,QAAA,IAAA;AACA,EAAAA,MAAA,aAAA,IAAA;AACA,EAAAA,MAAA,OAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,WAAA,IAAA;AACA,EAAAA,MAAA,gBAAA,IAAA;AACA,EAAAA,MAAA,aAAA,IAAA;AACA,EAAAA,MAAA,YAAA,IAAA;AACA,EAAAA,MAAA,WAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,QAAA,IAAA;AACA,EAAAA,MAAA,YAAA,IAAA;AACA,EAAAA,MAAA,oBAAA,IAAA;AACA,EAAAA,MAAA,UAAA,IAAA;AACA,EAAAA,MAAA,UAAA,IAAA;AACA,EAAAA,MAAA,UAAA,IAAA;AACA,EAAAA,MAAA,QAAA,IAAA;AACA,EAAAA,MAAA,UAAA,IAAA;AACA,EAAAA,MAAA,YAAA,IAAA;AACA,EAAAA,MAAA,OAAA,IAAA;AACA,EAAAA,MAAA,MAAA,IAAA;AACA,EAAAA,MAAA,MAAA,IAAA;AACA,EAAAA,MAAA,UAAA,IAAA;AACA,EAAAA,MAAA,YAAA,IAAA;AACA,EAAAA,MAAA,cAAA,IAAA;AACA,EAAAA,MAAA,YAAA,IAAA;AACA,EAAAA,MAAA,OAAA,IAAA;AACA,EAAAA,MAAA,mBAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,QAAA,IAAA;AACA,EAAAA,MAAA,QAAA,IAAA;AACA,EAAAA,MAAA,UAAA,IAAA;AACA,EAAAA,MAAA,QAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,cAAA,IAAA;AACA,EAAAA,MAAA,YAAA,IAAA;AACA,EAAAA,MAAA,aAAA,IAAA;AACA,EAAAA,MAAA,8BAAA,IAAA;AACA,EAAAA,MAAA,WAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,OAAA,IAAA;AACA,EAAAA,MAAA,OAAA,IAAA;AACA,EAAAA,MAAA,aAAA,IAAA;AACA,EAAAA,MAAA,QAAA,IAAA;AACA,EAAAA,MAAA,UAAA,IAAA;AACJ,GApMY,SAAA,OAoMX,CAAA,EAAA;ICpMW;CAAZ,SAAYC,kBAAe;AACvB,EAAAA,iBAAA,KAAA,IAAA;AACA,EAAAA,iBAAA,MAAA,IAAA;AACA,EAAAA,iBAAA,KAAA,IAAA;AACA,EAAAA,iBAAA,OAAA,IAAA;AACA,EAAAA,iBAAA,QAAA,IAAA;AACA,EAAAA,iBAAA,SAAA,IAAA;AACJ,GAPY,oBAAA,kBAOX,CAAA,EAAA;ICPW;CAAZ,SAAYC,eAAY;AACpB,EAAAA,cAAA,QAAA,IAAA;AACA,EAAAA,cAAA,SAAA,IAAA;AACA,EAAAA,cAAA,KAAA,IAAA;AACA,EAAAA,cAAA,UAAA,IAAA;AACA,EAAAA,cAAA,MAAA,IAAA;AACA,EAAAA,cAAA,OAAA,IAAA;AACA,EAAAA,cAAA,YAAA,IAAA;AACA,EAAAA,cAAA,QAAA,IAAA;AACA,EAAAA,cAAA,aAAA,IAAA;AACJ,GAVY,iBAAA,eAUX,CAAA,EAAA;ICVW;CAAZ,SAAYC,cAAW;AACnB,EAAAA,aAAA,KAAA,IAAA;AACA,EAAAA,aAAA,MAAA,IAAA;AACA,EAAAA,aAAA,KAAA,IAAA;AACA,EAAAA,aAAA,KAAA,IAAA;AACA,EAAAA,aAAA,MAAA,IAAA;AACA,EAAAA,aAAA,MAAA,IAAA;AACA,EAAAA,aAAA,MAAA,IAAA;AACJ,GARY,gBAAA,cAQX,CAAA,EAAA;", "names": ["_a", "_b", "_c", "AuthenticatorType", "AuthenticationFactor", "OAuth<PERSON><PERSON><PERSON>", "Browser", "CreditCard", "Flag", "ExecutionMethod", "ImageGravity", "ImageFormat"]}