{"version": 3, "sources": ["../../src/utils.ts"], "sourcesContent": ["import { isMainThread as _isMainThread, threadId } from 'node:worker_threads'\n\n// since vitest run all cases in worker thread, we should compatible with it to pass E2E tests\n\n// @ts-expect-error use Vitest\nexport const isInVitestEntryThread = threadId === 0 && process.env.VITEST\nexport const isMainThread = _isMainThread || isInVitestEntryThread\n"], "mappings": "AAAA,SAAS,gBAAgB,eAAe,gBAAgB;AAKjD,MAAM,wBAAwB,aAAa,KAAK,QAAQ,IAAI;AAC5D,MAAM,eAAe,iBAAiB;", "names": []}