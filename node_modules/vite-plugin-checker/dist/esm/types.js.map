{"version": 3, "sources": ["../../src/types.ts"], "sourcesContent": ["import type { Worker } from 'node:worker_threads'\nimport type { ESLint } from 'eslint'\nimport type * as Stylelint from 'stylelint'\nimport type { ConfigEnv, ErrorPayload } from 'vite'\nimport type { VlsOptions } from './checkers/vls/initParams.js'\n\n/* ----------------------------- userland plugin options ----------------------------- */\n\ninterface TsConfigOptions {\n  /**\n   * path to tsconfig.json file\n   */\n  tsconfigPath: string\n  /**\n   * root path of cwd\n   */\n  root: string\n  /**\n   * root path of cwd\n   */\n  buildMode: boolean\n}\n\n/**\n * TypeScript checker configuration\n * @default true\n */\nexport type TscConfig =\n  /**\n   * - set to `true` to enable type checking with default configuration\n   * - set to `false` to disable type checking, you can also remove `config.typescript` directly\n   */\n  boolean | Partial<TsConfigOptions>\n\n/** vue-tsc checker configuration */\nexport type VueTscConfig =\n  /**\n   * - set to `true` to enable type checking with default configuration\n   * - set to `false` to disable type checking, you can also remove `config.vueTsc` directly\n   */\n  boolean | Partial<Omit<TsConfigOptions, 'buildMode'>>\n\n/** vls checker configuration */\nexport type VlsConfig = boolean | DeepPartial<VlsOptions>\n\n/** ESLint checker configuration */\nexport type EslintConfig =\n  | false\n  | {\n      /**\n       * lintCommand will be executed at build mode, and will also be used as\n       * default config for dev mode when options.eslint.dev.eslint is nullable.\n       */\n      lintCommand: string\n      /**\n       * @default false\n       */\n      useFlatConfig?: boolean\n      dev?: Partial<{\n        /** You can override the options of translated from lintCommand. */\n        overrideConfig: ESLint.Options\n        /** which level of the diagnostic will be emitted from plugin */\n        logLevel: ('error' | 'warning')[]\n      }>\n    }\n\n/** Stylelint checker configuration */\nexport type StylelintConfig =\n  | false\n  | {\n      /**\n       * lintCommand will be executed at build mode, and will also be used as\n       * default config for dev mode when options.stylelint.dev.stylelint is nullable.\n       */\n      lintCommand: string\n      dev?: Partial<{\n        /** You can override the options of translated from lintCommand. */\n        overrideConfig: Stylelint.LinterOptions\n        /** which level of the diagnostic will be emitted from plugin */\n        logLevel: ('error' | 'warning')[]\n      }>\n    }\n\ntype BiomeCommand = 'lint' | 'check' | 'format' | 'ci'\n/** Biome checker configuration */\nexport type BiomeConfig =\n  | boolean\n  | {\n      /**\n       * Command will be used in dev and build mode, will be override\n       * if `dev.command` or `build.command` is set their mode.\n       */\n      command?: BiomeCommand\n      /**\n       * Flags of the command, will be override if `dev.flags`\n       * or `build.command` is set their mode.\n       * */\n      flags?: string\n      dev?: Partial<{\n        /** Command will be used in dev mode */\n        command: BiomeCommand\n        /** Flags of the command */\n        flags?: string\n        /** Which level of the diagnostic will be emitted from plugin */\n        logLevel: ('error' | 'warning' | 'info')[]\n      }>\n      build?: Partial<{\n        /** Command will be used in build mode */\n        command: BiomeCommand\n        /** Flags of the command */\n        flags?: string\n      }>\n    }\n\nexport enum DiagnosticLevel {\n  Warning = 0,\n  Error = 1,\n  Suggestion = 2,\n  Message = 3,\n}\n\ntype ErrorPayloadErr = ErrorPayload['err']\nexport interface DiagnosticToRuntime extends ErrorPayloadErr {\n  checkerId: string\n  level?: DiagnosticLevel\n}\n\nexport interface ClientDiagnosticPayload {\n  event: 'vite-plugin-checker:error'\n  data: {\n    checkerId: string\n    diagnostics: DiagnosticToRuntime[]\n  }\n}\n\nexport interface ClientReconnectPayload {\n  event: 'vite-plugin-checker:reconnect'\n  data: ClientDiagnosticPayload[]\n}\n\nexport type ClientPayload = ClientDiagnosticPayload | ClientReconnectPayload\n\n/** checkers shared configuration */\nexport interface SharedConfig {\n  /**\n   * Show overlay on UI view when there are errors or warnings in dev mode.\n   * - Set `true` to show overlay\n   * - Set `false` to disable overlay\n   * - Set with a object to customize overlay\n   *\n   * @defaultValue `true`\n   */\n  overlay:\n    | boolean\n    | {\n        /**\n         * Set this true if you want the overlay to default to being open if\n         * errors/warnings are found\n         * @defaultValue `true`\n         */\n        initialIsOpen?: boolean | 'error'\n        /**\n         * The position of the vite-plugin-checker badge to open and close\n         * the diagnostics panel\n         * @default `bl`\n         */\n        position?: 'tl' | 'tr' | 'bl' | 'br'\n        /**\n         * Use this to add extra style string to the badge button, the string format is\n         * [HTML element's style property](https://developer.mozilla.org/en-US/docs/Web/API/HTMLElement/style)\n         * For example, if you want to hide the badge,\n         * you can pass `display: none;` to the badgeStyle property\n         * @default no default value\n         */\n        badgeStyle?: string\n        /**\n         * Use this to add extra style string to the diagnostic panel, the string format is\n         * [HTML element's style property](https://developer.mozilla.org/en-US/docs/Web/API/HTMLElement/style)\n         * For example, if you want to change the opacity of the panel,\n         * you can pass `opacity: 0.8;` to the panelStyle property\n         * @default no default value\n         */\n        panelStyle?: string\n      }\n  /**\n   * stdout in terminal which starts the Vite server in dev mode.\n   * - Set `true` to enable\n   * - Set `false` to disable\n   *\n   * @defaultValue `true`\n   */\n  terminal: boolean\n  /**\n   * Enable checking in build mode\n   * @defaultValue `true`\n   */\n  enableBuild: boolean\n  /**\n   * Configure root directory of checkers\n   * @defaultValue no default value\n   */\n  root?: string\n}\n\nexport interface BuildInCheckers {\n  typescript: TscConfig\n  vueTsc: VueTscConfig\n  vls: VlsConfig\n  eslint: EslintConfig\n  stylelint: StylelintConfig\n  biome: BiomeConfig\n}\n\nexport type BuildInCheckerNames = keyof BuildInCheckers\n\nexport type PluginConfig = SharedConfig & BuildInCheckers\n\n/** Userland plugin configuration */\nexport type UserPluginConfig = Partial<PluginConfig>\n\n/* ----------------------------- worker actions ----------------------------- */\n\nexport enum ACTION_TYPES {\n  config = 'config',\n  configureServer = 'configureServer',\n  overlayError = 'overlayError',\n  console = 'console',\n  unref = 'unref',\n}\n\ninterface AbstractAction {\n  type: string\n  payload: unknown\n}\n\nexport interface OverlayErrorAction extends AbstractAction {\n  type: ACTION_TYPES.overlayError\n  /**\n   * send `ClientPayload` to raise error overlay\n   * send `null` to clear overlay for current checker\n   */\n  payload: ClientPayload\n}\n\nexport interface ConfigAction extends AbstractAction {\n  type: ACTION_TYPES.config\n  payload: ConfigActionPayload\n}\n\nexport interface ConfigureServerAction extends AbstractAction {\n  type: ACTION_TYPES.configureServer\n  payload: {\n    root: string\n  }\n}\n\nexport interface ConsoleAction extends AbstractAction {\n  type: ACTION_TYPES.console\n  payload: string\n}\n\nexport interface UnrefAction extends AbstractAction {\n  type: ACTION_TYPES.unref\n}\n\ninterface ConfigActionPayload {\n  enableOverlay: boolean\n  enableTerminal: boolean\n  env: ConfigEnv\n}\n\nexport type Action =\n  | ConfigAction\n  | ConfigureServerAction\n  | ConsoleAction\n  | OverlayErrorAction\n  | UnrefAction\n\n/* ----------------------------- internal types ----------------------------- */\n\n// prepare for create serve & build checker\n\nexport type BuildCheckBin = BuildCheckBinStr | BuildCheckBinFn\nexport type BuildCheckBinStr = [string, ReadonlyArray<string>]\nexport type BuildCheckBinFn = (\n  config: UserPluginConfig,\n) => [string, ReadonlyArray<string>]\n\nexport interface ConfigureServeChecker {\n  worker: Worker\n  config: (config: ConfigAction['payload']) => void\n  configureServer: (serverConfig: ConfigureServerAction['payload']) => void\n}\n\nexport interface ServeAndBuildChecker {\n  serve: ConfigureServeChecker\n  build: { buildBin: BuildCheckBin; buildFile?: string }\n}\n\n/**\n * create serve & build checker\n */\n\nexport interface ServeChecker<T extends BuildInCheckerNames = any> {\n  createDiagnostic: CreateDiagnostic<T>\n}\n\nexport interface CheckerDiagnostic {\n  config: (options: ConfigActionPayload) => unknown\n  configureServer: (options: { root: string }) => unknown\n}\n\nexport type CreateDiagnostic<T extends BuildInCheckerNames = any> = (\n  config: Pick<BuildInCheckers, T> & SharedConfig,\n) => CheckerDiagnostic\n\n/* ----------------------------- generic utility types ----------------------------- */\n\nexport type DeepPartial<T> = {\n  [P in keyof T]?: DeepPartial<T[P]>\n}\n"], "mappings": "AAkHO,IAAK,kBAAL,kBAAKA,qBAAL;AACL,EAAAA,kCAAA,aAAU,KAAV;AACA,EAAAA,kCAAA,WAAQ,KAAR;AACA,EAAAA,kCAAA,gBAAa,KAAb;AACA,EAAAA,kCAAA,aAAU,KAAV;AAJU,SAAAA;AAAA,GAAA;AA4GL,IAAK,eAAL,kBAAKC,kBAAL;AACL,EAAAA,cAAA,YAAS;AACT,EAAAA,cAAA,qBAAkB;AAClB,EAAAA,cAAA,kBAAe;AACf,EAAAA,cAAA,aAAU;AACV,EAAAA,cAAA,WAAQ;AALE,SAAAA;AAAA,GAAA;", "names": ["DiagnosticLevel", "ACTION_TYPES"]}