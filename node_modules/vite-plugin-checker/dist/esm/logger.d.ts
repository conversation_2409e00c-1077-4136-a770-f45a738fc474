import { SourceLocation } from '@babel/code-frame';
import { DiagnosticLevel, DiagnosticToRuntime, ClientDiagnosticPayload } from './types.js';
import { ESLint } from 'eslint';
import Stylelint__default from 'stylelint';
import { Diagnostic as Diagnostic$1, PublishDiagnosticsParams } from 'vscode-languageclient/node';
import { Diagnostic } from 'typescript';
import 'node:worker_threads';
import 'vite';
import './checkers/vls/initParams.js';
import 'vscode-uri';
import 'vscode-languageserver/node';

interface NormalizedDiagnostic {
    /** error message */
    message?: string;
    /** error conclusion */
    conclusion?: string;
    /** error stack */
    stack?: string | string[];
    /** file name */
    id?: string;
    /** checker diagnostic source */
    checker: string;
    /** raw code frame generated by @babel/code-frame */
    codeFrame?: string;
    /** code frame, but striped */
    stripedCodeFrame?: string;
    /** error code location */
    loc?: SourceLocation;
    /** error level */
    level?: DiagnosticLevel;
}
declare function filterLogLevel(diagnostics: NormalizedDiagnostic, level?: DiagnosticLevel[]): NormalizedDiagnostic | null;
declare function filterLogLevel(diagnostics: NormalizedDiagnostic[], level?: DiagnosticLevel[]): NormalizedDiagnostic[];
declare function diagnosticToTerminalLog(d: NormalizedDiagnostic, name?: 'TypeScript' | 'vue-tsc' | 'VLS' | 'ESLint' | 'Stylelint' | 'Biome'): string;
declare function diagnosticToRuntimeError(d: NormalizedDiagnostic): DiagnosticToRuntime;
declare function diagnosticToRuntimeError(d: NormalizedDiagnostic[]): DiagnosticToRuntime[];
declare function toClientPayload(id: string, diagnostics: DiagnosticToRuntime[]): ClientDiagnosticPayload;
declare function wrapCheckerSummary(checkerName: string, rawSummary: string): string;
declare function composeCheckerSummary(checkerName: string, errorCount: number, warningCount: number): string;
declare function normalizeTsDiagnostic(d: Diagnostic): NormalizedDiagnostic;
declare function normalizeLspDiagnostic({ diagnostic, absFilePath, fileText, }: {
    diagnostic: Diagnostic$1;
    absFilePath: string;
    fileText: string;
}): NormalizedDiagnostic;
declare function normalizePublishDiagnosticParams(publishDiagnostics: PublishDiagnosticsParams): Promise<NormalizedDiagnostic[]>;
declare function uriToAbsPath(documentUri: string): string;
declare function normalizeVueTscDiagnostic(d: Diagnostic): NormalizedDiagnostic;
declare function normalizeEslintDiagnostic(diagnostic: ESLint.LintResult): NormalizedDiagnostic[];
declare function normalizeStylelintDiagnostic(diagnostic: Stylelint__default.LintResult): NormalizedDiagnostic[];
declare function ensureCall(callback: CallableFunction): void;
declare function consoleLog(value: string): void;

export { NormalizedDiagnostic, composeCheckerSummary, consoleLog, diagnosticToRuntimeError, diagnosticToTerminalLog, ensureCall, filterLogLevel, normalizeEslintDiagnostic, normalizeLspDiagnostic, normalizePublishDiagnosticParams, normalizeStylelintDiagnostic, normalizeTsDiagnostic, normalizeVueTscDiagnostic, toClientPayload, uriToAbsPath, wrapCheckerSummary };
