{"version": 3, "sources": ["../../../../src/checkers/vueTsc/prepareVueTsc.ts", "../../../../../../node_modules/.pnpm/tsup@6.7.0_postcss@8.4.38_typescript@5.5.3/node_modules/tsup/assets/cjs_shims.js"], "sourcesContent": ["import { access, readFile, rm, writeFile } from 'node:fs/promises'\nimport { createRequire } from 'node:module'\nimport path, { dirname } from 'node:path'\nimport { fileURLToPath } from 'node:url'\nimport fsExtra from 'fs-extra'\n\nconst { copy, mkdir } = fsExtra\nconst _require = createRequire(import.meta.url)\n\n// isomorphic __dirname https://antfu.me/posts/isomorphic-dirname\nconst _filename = fileURLToPath(import.meta.url)\nconst _dirname = dirname(_filename)\nconst vueTscDir = dirname(_require.resolve('vue-tsc/package.json'))\nconst proxyApiPath = _require.resolve(\n  '@volar/typescript/lib/node/proxyCreateProgram',\n  {\n    paths: [vueTscDir],\n  },\n)\nconst extraSupportedExtensions = ['.vue']\n\nexport async function prepareVueTsc() {\n  // 1. copy typescript to folder\n  const targetTsDir = path.resolve(_dirname, 'typescript-vue-tsc')\n  const vueTscFlagFile = path.resolve(targetTsDir, 'vue-tsc-resolve-path')\n  const currTsVersion = _require('typescript/package.json').version\n\n  const tsMajorVersion = Number(currTsVersion.split('.')[0])\n  if (tsMajorVersion < 5) {\n    throw new Error(\n      \"\\x1b[35m[vite-plugin-checker] Since 0.7.0, vue-tsc checkers requires TypeScript 5.0.0 or newer version.\\nPlease upgrade TypeScript, or use v0.6.4 which works with vue-tsc^1 if you can't upgrade. Check the pull request https://github.com/fi3ework/vite-plugin-checker/pull/327 for detail.\\x1b[39m\\n\",\n    )\n  }\n\n  let shouldBuildFixture = true\n  try {\n    await access(targetTsDir)\n    const targetTsVersion = _require(\n      path.resolve(targetTsDir, 'package.json'),\n    ).version\n    // check fixture versions before re-use\n    await access(vueTscFlagFile)\n    const fixtureFlagContent = await readFile(vueTscFlagFile, 'utf8')\n    if (\n      targetTsVersion === currTsVersion &&\n      fixtureFlagContent === proxyApiPath\n    ) {\n      shouldBuildFixture = false\n    }\n  } catch (e) {\n    // no matter what error, we should rebuild the fixture\n    shouldBuildFixture = true\n  }\n\n  if (shouldBuildFixture) {\n    await rm(targetTsDir, { force: true, recursive: true })\n    await mkdir(targetTsDir)\n    const sourceTsDir = path.resolve(_require.resolve('typescript'), '../..')\n    await copy(sourceTsDir, targetTsDir)\n    await writeFile(vueTscFlagFile, proxyApiPath)\n\n    // 2. sync modification of lib/tsc.js with vue-tsc\n    await overrideTscJs(\n      _require.resolve(path.resolve(targetTsDir, 'lib/typescript.js')),\n    )\n  }\n\n  return { targetTsDir }\n}\n\nasync function overrideTscJs(tscJsPath: string) {\n  const languagePluginsFile = path.resolve(_dirname, 'languagePlugins.cjs')\n  let tsc = await readFile(tscJsPath, 'utf8')\n  // #region copied from https://github.com/volarjs/volar.js/blob/630f31118d3986c00cc730eb83cd896709fd547e/packages/typescript/lib/quickstart/runTsc.ts\n  // add *.vue files to allow extensions\n  const extsText = extraSupportedExtensions.map((ext) => `\"${ext}\"`).join(', ')\n  // tsc = replace(tsc, /supportedTSExtensions = .*(?=;)/, (s) => `${s}.concat([[${extsText}]])`)\n  tsc = replace(\n    tsc,\n    /supportedTSExtensions = .*(?=;)/,\n    (s) =>\n      // biome-ignore lint/style/useTemplate: <explanation>\n      s +\n      `.map((group, i) => i === 0 ? group.splice(0, 0, ${extsText}) && group : group)`,\n  )\n  tsc = replace(\n    tsc,\n    /supportedJSExtensions = .*(?=;)/,\n    (s) =>\n      // biome-ignore lint/style/useTemplate: <explanation>\n      s +\n      `.map((group, i) => i === 0 ? group.splice(0, 0, ${extsText}) && group : group)`,\n  )\n  tsc = replace(\n    tsc,\n    /allSupportedExtensions = .*(?=;)/,\n    (s) =>\n      // biome-ignore lint/style/useTemplate: <explanation>\n      s +\n      `.map((group, i) => i === 0 ? group.splice(0, 0, ${extsText}) && group : group)`,\n  )\n\n  const extsText2 = extraSupportedExtensions.map((ext) => `\"${ext}\"`).join(', ')\n  tsc = replace(\n    tsc,\n    /function changeExtension\\(/,\n    (s) =>\n      // biome-ignore lint/style/useTemplate: <explanation>\n      `function changeExtension(path, newExtension) {\n\t\t\t\t\treturn [${extsText2}].some(ext => path.endsWith(ext))\n\t\t\t\t\t\t? path + newExtension\n\t\t\t\t\t\t: _changeExtension(path, newExtension)\n\t\t\t\t\t}\\n` + s.replace('changeExtension', '_changeExtension'),\n  )\n\n  // proxy createProgram\n  tsc = replace(\n    tsc,\n    /function createProgram\\(.+\\) {/,\n    (s) =>\n      `var createProgram = require(${JSON.stringify(\n        proxyApiPath,\n      )}).proxyCreateProgram(${[\n        'new Proxy({}, { get(_target, p, _receiver) { return eval(p); } } )',\n        '_createProgram',\n        `require(${JSON.stringify(languagePluginsFile)}).getLanguagePlugins`,\n      ].join(', ')});\\n${s.replace('createProgram', '_createProgram')}`,\n  )\n\n  function replace(\n    _text: string,\n    ...[search, replace]: Parameters<string['replace']>\n  ) {\n    const before = _text\n    const text = _text.replace(search, replace)\n    const after = text\n    if (after === before) {\n      throw `Search string not found: ${JSON.stringify(search.toString())}`\n    }\n    return after\n  }\n  // #endregion\n\n  await writeFile(tscJsPath, tsc)\n}\n", "// Shim globals in cjs bundle\n// There's a weird bug that esbuild will always inject importMetaUrl\n// if we export it as `const importMetaUrl = ... __filename ...`\n// But using a function will not cause this issue\n\nconst getImportMetaUrl = () =>\n  typeof document === 'undefined'\n    ? new URL('file:' + __filename).href\n    : (document.currentScript && document.currentScript.src) ||\n      new URL('main.js', document.baseURI).href\n\nexport const importMetaUrl = /* @__PURE__ */ getImportMetaUrl()\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;ACKA,IAAM,mBAAmB,MACvB,OAAO,aAAa,cAChB,IAAI,IAAI,UAAU,UAAU,EAAE,OAC7B,SAAS,iBAAiB,SAAS,cAAc,OAClD,IAAI,IAAI,WAAW,SAAS,OAAO,EAAE;AAEpC,IAAM,gBAAgC,iCAAiB;ADX9D,sBAAgD;AAChD,yBAA8B;AAC9B,uBAA8B;AAC9B,sBAA8B;AAC9B,sBAAoB;AAEpB,MAAM,EAAE,MAAM,MAAM,IAAI,gBAAAA;AACxB,MAAM,eAAW,kCAAc,aAAe;AAG9C,MAAM,gBAAY,+BAAc,aAAe;AAC/C,MAAM,eAAW,0BAAQ,SAAS;AAClC,MAAM,gBAAY,0BAAQ,SAAS,QAAQ,sBAAsB,CAAC;AAClE,MAAM,eAAe,SAAS;AAAA,EAC5B;AAAA,EACA;AAAA,IACE,OAAO,CAAC,SAAS;AAAA,EACnB;AACF;AACA,MAAM,2BAA2B,CAAC,MAAM;AAExC,eAAsB,gBAAgB;AAEpC,QAAM,cAAc,iBAAAC,QAAK,QAAQ,UAAU,oBAAoB;AAC/D,QAAM,iBAAiB,iBAAAA,QAAK,QAAQ,aAAa,sBAAsB;AACvE,QAAM,gBAAgB,SAAS,yBAAyB,EAAE;AAE1D,QAAM,iBAAiB,OAAO,cAAc,MAAM,GAAG,EAAE,CAAC,CAAC;AACzD,MAAI,iBAAiB,GAAG;AACtB,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAEA,MAAI,qBAAqB;AACzB,MAAI;AACF,cAAM,wBAAO,WAAW;AACxB,UAAM,kBAAkB;AAAA,MACtB,iBAAAA,QAAK,QAAQ,aAAa,cAAc;AAAA,IAC1C,EAAE;AAEF,cAAM,wBAAO,cAAc;AAC3B,UAAM,qBAAqB,UAAM,0BAAS,gBAAgB,MAAM;AAChE,QACE,oBAAoB,iBACpB,uBAAuB,cACvB;AACA,2BAAqB;AAAA,IACvB;AAAA,EACF,SAAS,GAAP;AAEA,yBAAqB;AAAA,EACvB;AAEA,MAAI,oBAAoB;AACtB,cAAM,oBAAG,aAAa,EAAE,OAAO,MAAM,WAAW,KAAK,CAAC;AACtD,UAAM,MAAM,WAAW;AACvB,UAAM,cAAc,iBAAAA,QAAK,QAAQ,SAAS,QAAQ,YAAY,GAAG,OAAO;AACxE,UAAM,KAAK,aAAa,WAAW;AACnC,cAAM,2BAAU,gBAAgB,YAAY;AAG5C,UAAM;AAAA,MACJ,SAAS,QAAQ,iBAAAA,QAAK,QAAQ,aAAa,mBAAmB,CAAC;AAAA,IACjE;AAAA,EACF;AAEA,SAAO,EAAE,YAAY;AACvB;AAEA,eAAe,cAAc,WAAmB;AAC9C,QAAM,sBAAsB,iBAAAA,QAAK,QAAQ,UAAU,qBAAqB;AACxE,MAAI,MAAM,UAAM,0BAAS,WAAW,MAAM;AAG1C,QAAM,WAAW,yBAAyB,IAAI,CAAC,QAAQ,IAAI,MAAM,EAAE,KAAK,IAAI;AAE5E,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,CAAC;AAAA;AAAA,MAEC,IACA,mDAAmD;AAAA;AAAA,EACvD;AACA,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,CAAC;AAAA;AAAA,MAEC,IACA,mDAAmD;AAAA;AAAA,EACvD;AACA,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,CAAC;AAAA;AAAA,MAEC,IACA,mDAAmD;AAAA;AAAA,EACvD;AAEA,QAAM,YAAY,yBAAyB,IAAI,CAAC,QAAQ,IAAI,MAAM,EAAE,KAAK,IAAI;AAC7E,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,CAAC;AAAA;AAAA,MAEC;AAAA,eACS;AAAA;AAAA;AAAA;AAAA,IAGH,EAAE,QAAQ,mBAAmB,kBAAkB;AAAA;AAAA,EACzD;AAGA,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,CAAC,MACC,+BAA+B,KAAK;AAAA,MAClC;AAAA,IACF,yBAAyB;AAAA,MACvB;AAAA,MACA;AAAA,MACA,WAAW,KAAK,UAAU,mBAAmB;AAAA,IAC/C,EAAE,KAAK,IAAI;AAAA,EAAQ,EAAE,QAAQ,iBAAiB,gBAAgB;AAAA,EAClE;AAEA,WAAS,QACP,UACG,CAAC,QAAQC,QAAO,GACnB;AACA,UAAM,SAAS;AACf,UAAM,OAAO,MAAM,QAAQ,QAAQA,QAAO;AAC1C,UAAM,QAAQ;AACd,QAAI,UAAU,QAAQ;AACpB,YAAM,4BAA4B,KAAK,UAAU,OAAO,SAAS,CAAC;AAAA,IACpE;AACA,WAAO;AAAA,EACT;AAGA,YAAM,2BAAU,WAAW,GAAG;AAChC;", "names": ["fsExtra", "path", "replace"]}