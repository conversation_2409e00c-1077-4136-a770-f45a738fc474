{"version": 3, "sources": ["../../src/worker.ts"], "sourcesContent": ["import { Worker, parentPort, workerData } from 'node:worker_threads'\nimport type { ConfigEnv } from 'vite'\n\nimport { ACTION_TYPES } from './types.js'\n\nimport type {\n  BuildCheckBin,\n  BuildInCheckers,\n  CheckerDiagnostic,\n  ConfigAction,\n  ConfigureServe<PERSON>he<PERSON>,\n  ConfigureServerAction,\n  ServeAndBuild<PERSON>hecker,\n  Serve<PERSON>he<PERSON>,\n  SharedConfig,\n  UnrefAction,\n} from './types.js'\n\ninterface WorkerScriptOptions {\n  absFilename: string\n  buildBin: BuildCheckBin\n  serverChecker: Serve<PERSON><PERSON><PERSON>\n}\n\nexport interface Script<T> {\n  mainScript: () => (\n    config: T & SharedConfig,\n    env: ConfigEnv,\n  ) => ServeAndBuildChecker\n  workerScript: () => void\n}\n\nexport function createScript<T extends Partial<BuildInCheckers>>({\n  absFilename,\n  buildBin,\n  serverChecker,\n}: WorkerScriptOptions): Script<T> {\n  type CheckerConfig = T & SharedConfig\n\n  return {\n    mainScript: () => {\n      // initialized in main thread\n      const createWorker = (\n        checkerConfig: CheckerConfig,\n        env: ConfigEnv,\n      ): ConfigureServeChecker => {\n        const isBuild = env.command === 'build'\n        const worker = new Worker(absFilename, {\n          workerData: { env, checkerConfig },\n        })\n\n        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions\n        return {\n          worker,\n          config: (config) => {\n            if (isBuild) return // just run the command\n\n            const configAction: ConfigAction = {\n              type: ACTION_TYPES.config,\n              payload: config,\n            }\n            worker.postMessage(configAction)\n          },\n          configureServer: (serverConfig) => {\n            const configureServerAction: ConfigureServerAction = {\n              type: ACTION_TYPES.configureServer,\n              payload: serverConfig,\n            }\n\n            worker.postMessage(configureServerAction)\n          },\n        }\n      }\n\n      return (config, env) => {\n        return {\n          serve: createWorker(config, env),\n          build: { buildBin },\n        }\n      }\n    },\n    workerScript: () => {\n      // runs in worker thread\n      let diagnostic: CheckerDiagnostic | null = null\n      if (!parentPort)\n        throw Error('should have parentPort as file runs in worker thread')\n      const isBuild = workerData.env.command === 'build'\n      // only run bin command and do not listen message in build mode\n\n      const port = parentPort.on(\n        'message',\n        (action: ConfigAction | ConfigureServerAction | UnrefAction) => {\n          switch (action.type) {\n            case ACTION_TYPES.config: {\n              const checkerConfig: T & SharedConfig = workerData.checkerConfig\n              diagnostic = serverChecker.createDiagnostic(checkerConfig)\n              diagnostic.config(action.payload)\n              break\n            }\n            case ACTION_TYPES.configureServer:\n              if (!diagnostic)\n                throw Error(\n                  'diagnostic should be initialized in `config` hook of Vite',\n                )\n              diagnostic.configureServer(action.payload)\n              break\n            case ACTION_TYPES.unref:\n              port.unref()\n              break\n          }\n        },\n      )\n\n      if (isBuild) {\n        port.unref()\n      }\n    },\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iCAA+C;AAG/C,mBAA6B;AA6BtB,SAAS,aAAiD;AAAA,EAC/D;AAAA,EACA;AAAA,EACA;AACF,GAAmC;AAGjC,SAAO;AAAA,IACL,YAAY,MAAM;AAEhB,YAAM,eAAe,CACnB,eACA,QAC0B;AAC1B,cAAM,UAAU,IAAI,YAAY;AAChC,cAAM,SAAS,IAAI,kCAAO,aAAa;AAAA,UACrC,YAAY,EAAE,KAAK,cAAc;AAAA,QACnC,CAAC;AAGD,eAAO;AAAA,UACL;AAAA,UACA,QAAQ,CAAC,WAAW;AAClB,gBAAI;AAAS;AAEb,kBAAM,eAA6B;AAAA,cACjC,MAAM,0BAAa;AAAA,cACnB,SAAS;AAAA,YACX;AACA,mBAAO,YAAY,YAAY;AAAA,UACjC;AAAA,UACA,iBAAiB,CAAC,iBAAiB;AACjC,kBAAM,wBAA+C;AAAA,cACnD,MAAM,0BAAa;AAAA,cACnB,SAAS;AAAA,YACX;AAEA,mBAAO,YAAY,qBAAqB;AAAA,UAC1C;AAAA,QACF;AAAA,MACF;AAEA,aAAO,CAAC,QAAQ,QAAQ;AACtB,eAAO;AAAA,UACL,OAAO,aAAa,QAAQ,GAAG;AAAA,UAC/B,OAAO,EAAE,SAAS;AAAA,QACpB;AAAA,MACF;AAAA,IACF;AAAA,IACA,cAAc,MAAM;AAElB,UAAI,aAAuC;AAC3C,UAAI,CAAC;AACH,cAAM,MAAM,sDAAsD;AACpE,YAAM,UAAU,sCAAW,IAAI,YAAY;AAG3C,YAAM,OAAO,sCAAW;AAAA,QACtB;AAAA,QACA,CAAC,WAA+D;AAC9D,kBAAQ,OAAO,MAAM;AAAA,YACnB,KAAK,0BAAa,QAAQ;AACxB,oBAAM,gBAAkC,sCAAW;AACnD,2BAAa,cAAc,iBAAiB,aAAa;AACzD,yBAAW,OAAO,OAAO,OAAO;AAChC;AAAA,YACF;AAAA,YACA,KAAK,0BAAa;AAChB,kBAAI,CAAC;AACH,sBAAM;AAAA,kBACJ;AAAA,gBACF;AACF,yBAAW,gBAAgB,OAAO,OAAO;AACzC;AAAA,YACF,KAAK,0BAAa;AAChB,mBAAK,MAAM;AACX;AAAA,UACJ;AAAA,QACF;AAAA,MACF;AAEA,UAAI,SAAS;AACX,aAAK,MAAM;AAAA,MACb;AAAA,IACF;AAAA,EACF;AACF;", "names": []}