import { createRequire } from 'node:module';
import { c as normalize } from './nuxi.610c92ff.mjs';

function getModulePaths(paths) {
  return [].concat(
    global.__NUXT_PREPATHS__,
    paths,
    process.cwd(),
    global.__NUXT_PATHS__
  ).filter(Boolean);
}
const _require = createRequire(process.cwd());
function resolveModule(id, paths) {
  return normalize(_require.resolve(id, { paths: getModulePaths(paths) }));
}
function requireModule(id, paths) {
  return _require(resolveModule(id, paths));
}
function tryRequireModule(id, paths) {
  try {
    return requireModule(id, paths);
  } catch {
    return null;
  }
}

export { tryRequireModule as t };
