import { execSync } from 'node:child_process';
import { existsSync } from 'node:fs';
import { f as findup } from './nuxi.d2904056.mjs';
import { r as resolve } from './nuxi.610c92ff.mjs';

const packageManagerLocks = {
  yarn: "yarn.lock",
  npm: "package-lock.json",
  pnpm: "pnpm-lock.yaml",
  bun: "bun.lockb"
};
function getPackageManager(rootDir) {
  return findup(rootDir, (dir) => {
    for (const name in packageManagerLocks) {
      const path = packageManagerLocks[name];
      if (path && existsSync(resolve(dir, path))) {
        return name;
      }
    }
  });
}
function getPackageManagerVersion(name) {
  return execSync(`${name} --version`).toString("utf8").trim();
}

export { getPackageManagerVersion as a, getPackageManager as g, packageManagerLocks as p };
