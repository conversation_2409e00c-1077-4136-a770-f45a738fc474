import { promises, existsSync } from 'node:fs';
import { c as consola } from './nuxi.3e201632.mjs';
import 'node:util';
import 'node:path';
import 'node:process';
import 'node:tty';
import { j as join, d as dirname } from './nuxi.610c92ff.mjs';

async function clearDir(path, exclude) {
  if (!exclude) {
    await promises.rm(path, { recursive: true, force: true });
  } else if (existsSync(path)) {
    const files = await promises.readdir(path);
    await Promise.all(
      files.map(async (name) => {
        if (!exclude.includes(name)) {
          await promises.rm(join(path, name), { recursive: true, force: true });
        }
      })
    );
  }
  await promises.mkdir(path, { recursive: true });
}
function clearBuildDir(path) {
  return clearDir(path, ["cache", "analyze"]);
}
async function rmRecursive(paths) {
  await Promise.all(
    paths.filter((p) => typeof p === "string").map(async (path) => {
      consola.debug("Removing recursive path", path);
      await promises.rm(path, { recursive: true, force: true }).catch(() => {
      });
    })
  );
}
async function touchFile(path) {
  const time = /* @__PURE__ */ new Date();
  await promises.utimes(path, time, time).catch(() => {
  });
}
function findup(rootDir, fn) {
  let dir = rootDir;
  while (dir !== dirname(dir)) {
    const res = fn(dir);
    if (res) {
      return res;
    }
    dir = dirname(dir);
  }
  return null;
}

export { clearBuildDir as a, clearDir as c, findup as f, rmRecursive as r, touchFile as t };
