import { promises, statSync } from 'node:fs';
import { r as resolvePath } from './nuxi.4d6fa6e6.mjs';
import { i as isAbsolute, r as resolve, j as join, d as dirname } from './nuxi.610c92ff.mjs';

const m=Symbol.for("__confbox_fmt__"),k=/^(\s+)/,v$1=/(\s+)$/;function x(e,t={}){const n=t.indent===void 0&&t.preserveIndentation!==!1&&e.slice(0,t?.sampleSize||1024),i=t.preserveWhitespace===!1?void 0:{start:k.exec(e)?.[0]||"",end:v$1.exec(e)?.[0]||""};return {sample:n,whiteSpace:i}}function N$1(e,t,n){!t||typeof t!="object"||Object.defineProperty(t,m,{enumerable:!1,configurable:!0,writable:!0,value:x(e,n)});}

function Y(n,l=!1){const g=n.length;let e=0,t="",T=0,f=16,O=0,p=0,A=0,B=0,s=0;function F(i,c){let u=0,b=0;for(;u<i||!c;){let m=n.charCodeAt(e);if(m>=48&&m<=57)b=b*16+m-48;else if(m>=65&&m<=70)b=b*16+m-65+10;else if(m>=97&&m<=102)b=b*16+m-97+10;else break;e++,u++;}return u<i&&(b=-1),b}function j(i){e=i,t="",T=0,f=16,s=0;}function w(){let i=e;if(n.charCodeAt(e)===48)e++;else for(e++;e<n.length&&N(n.charCodeAt(e));)e++;if(e<n.length&&n.charCodeAt(e)===46)if(e++,e<n.length&&N(n.charCodeAt(e)))for(e++;e<n.length&&N(n.charCodeAt(e));)e++;else return s=3,n.substring(i,e);let c=e;if(e<n.length&&(n.charCodeAt(e)===69||n.charCodeAt(e)===101))if(e++,(e<n.length&&n.charCodeAt(e)===43||n.charCodeAt(e)===45)&&e++,e<n.length&&N(n.charCodeAt(e))){for(e++;e<n.length&&N(n.charCodeAt(e));)e++;c=e;}else s=3;return n.substring(i,c)}function L(){let i="",c=e;for(;;){if(e>=g){i+=n.substring(c,e),s=2;break}const u=n.charCodeAt(e);if(u===34){i+=n.substring(c,e),e++;break}if(u===92){if(i+=n.substring(c,e),e++,e>=g){s=2;break}switch(n.charCodeAt(e++)){case 34:i+='"';break;case 92:i+="\\";break;case 47:i+="/";break;case 98:i+="\b";break;case 102:i+="\f";break;case 110:i+=`
`;break;case 114:i+="\r";break;case 116:i+="	";break;case 117:const m=F(4,!0);m>=0?i+=String.fromCharCode(m):s=4;break;default:s=5;}c=e;continue}if(u>=0&&u<=31)if(_(u)){i+=n.substring(c,e),s=2;break}else s=6;e++;}return i}function U(){if(t="",s=0,T=e,p=O,B=A,e>=g)return T=g,f=17;let i=n.charCodeAt(e);if(D(i)){do e++,t+=String.fromCharCode(i),i=n.charCodeAt(e);while(D(i));return f=15}if(_(i))return e++,t+=String.fromCharCode(i),i===13&&n.charCodeAt(e)===10&&(e++,t+=`
`),O++,A=e,f=14;switch(i){case 123:return e++,f=1;case 125:return e++,f=2;case 91:return e++,f=3;case 93:return e++,f=4;case 58:return e++,f=6;case 44:return e++,f=5;case 34:return e++,t=L(),f=10;case 47:const c=e-1;if(n.charCodeAt(e+1)===47){for(e+=2;e<g&&!_(n.charCodeAt(e));)e++;return t=n.substring(c,e),f=12}if(n.charCodeAt(e+1)===42){e+=2;const u=g-1;let b=!1;for(;e<u;){const m=n.charCodeAt(e);if(m===42&&n.charCodeAt(e+1)===47){e+=2,b=!0;break}e++,_(m)&&(m===13&&n.charCodeAt(e)===10&&e++,O++,A=e);}return b||(e++,s=1),t=n.substring(c,e),f=13}return t+=String.fromCharCode(i),e++,f=16;case 45:if(t+=String.fromCharCode(i),e++,e===g||!N(n.charCodeAt(e)))return f=16;case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:return t+=w(),f=11;default:for(;e<g&&E(i);)e++,i=n.charCodeAt(e);if(T!==e){switch(t=n.substring(T,e),t){case"true":return f=8;case"false":return f=9;case"null":return f=7}return f=16}return t+=String.fromCharCode(i),e++,f=16}}function E(i){if(D(i)||_(i))return !1;switch(i){case 125:case 93:case 123:case 91:case 34:case 58:case 44:case 47:return !1}return !0}function r(){let i;do i=U();while(i>=12&&i<=15);return i}return {setPosition:j,getPosition:()=>e,scan:l?r:U,getToken:()=>f,getTokenValue:()=>t,getTokenOffset:()=>T,getTokenLength:()=>e-T,getTokenStartLine:()=>p,getTokenStartCharacter:()=>T-B,getTokenError:()=>s}}function D(n){return n===32||n===9}function _(n){return n===10||n===13}function N(n){return n>=48&&n<=57}var W;((function(n){n[n.lineFeed=10]="lineFeed",n[n.carriageReturn=13]="carriageReturn",n[n.space=32]="space",n[n._0=48]="_0",n[n._1=49]="_1",n[n._2=50]="_2",n[n._3=51]="_3",n[n._4=52]="_4",n[n._5=53]="_5",n[n._6=54]="_6",n[n._7=55]="_7",n[n._8=56]="_8",n[n._9=57]="_9",n[n.a=97]="a",n[n.b=98]="b",n[n.c=99]="c",n[n.d=100]="d",n[n.e=101]="e",n[n.f=102]="f",n[n.g=103]="g",n[n.h=104]="h",n[n.i=105]="i",n[n.j=106]="j",n[n.k=107]="k",n[n.l=108]="l",n[n.m=109]="m",n[n.n=110]="n",n[n.o=111]="o",n[n.p=112]="p",n[n.q=113]="q",n[n.r=114]="r",n[n.s=115]="s",n[n.t=116]="t",n[n.u=117]="u",n[n.v=118]="v",n[n.w=119]="w",n[n.x=120]="x",n[n.y=121]="y",n[n.z=122]="z",n[n.A=65]="A",n[n.B=66]="B",n[n.C=67]="C",n[n.D=68]="D",n[n.E=69]="E",n[n.F=70]="F",n[n.G=71]="G",n[n.H=72]="H",n[n.I=73]="I",n[n.J=74]="J",n[n.K=75]="K",n[n.L=76]="L",n[n.M=77]="M",n[n.N=78]="N",n[n.O=79]="O",n[n.P=80]="P",n[n.Q=81]="Q",n[n.R=82]="R",n[n.S=83]="S",n[n.T=84]="T",n[n.U=85]="U",n[n.V=86]="V",n[n.W=87]="W",n[n.X=88]="X",n[n.Y=89]="Y",n[n.Z=90]="Z",n[n.asterisk=42]="asterisk",n[n.backslash=92]="backslash",n[n.closeBrace=125]="closeBrace",n[n.closeBracket=93]="closeBracket",n[n.colon=58]="colon",n[n.comma=44]="comma",n[n.dot=46]="dot",n[n.doubleQuote=34]="doubleQuote",n[n.minus=45]="minus",n[n.openBrace=123]="openBrace",n[n.openBracket=91]="openBracket",n[n.plus=43]="plus",n[n.slash=47]="slash",n[n.formFeed=12]="formFeed",n[n.tab=9]="tab";}))(W||(W={})),new Array(20).fill(0).map((n,l)=>" ".repeat(l));const v=200;new Array(v).fill(0).map((n,l)=>`
`+" ".repeat(l)),new Array(v).fill(0).map((n,l)=>"\r"+" ".repeat(l)),new Array(v).fill(0).map((n,l)=>`\r
`+" ".repeat(l)),new Array(v).fill(0).map((n,l)=>`
`+"	".repeat(l)),new Array(v).fill(0).map((n,l)=>"\r"+"	".repeat(l)),new Array(v).fill(0).map((n,l)=>`\r
`+"	".repeat(l));var V;(function(n){n.DEFAULT={allowTrailingComma:!1};})(V||(V={}));function Z(n,l=[],g=V.DEFAULT){let e=null,t=[];const T=[];function f(p){Array.isArray(t)?t.push(p):e!==null&&(t[e]=p);}return $(n,{onObjectBegin:()=>{const p={};f(p),T.push(t),t=p,e=null;},onObjectProperty:p=>{e=p;},onObjectEnd:()=>{t=T.pop();},onArrayBegin:()=>{const p=[];f(p),T.push(t),t=p,e=null;},onArrayEnd:()=>{t=T.pop();},onLiteralValue:f,onError:(p,A,B)=>{l.push({error:p,offset:A,length:B});}},g),t[0]}function $(n,l,g=V.DEFAULT){const e=Y(n,!1),t=[];function T(k){return k?()=>k(e.getTokenOffset(),e.getTokenLength(),e.getTokenStartLine(),e.getTokenStartCharacter()):()=>!0}function f(k){return k?()=>k(e.getTokenOffset(),e.getTokenLength(),e.getTokenStartLine(),e.getTokenStartCharacter(),()=>t.slice()):()=>!0}function O(k){return k?o=>k(o,e.getTokenOffset(),e.getTokenLength(),e.getTokenStartLine(),e.getTokenStartCharacter()):()=>!0}function p(k){return k?o=>k(o,e.getTokenOffset(),e.getTokenLength(),e.getTokenStartLine(),e.getTokenStartCharacter(),()=>t.slice()):()=>!0}const A=f(l.onObjectBegin),B=p(l.onObjectProperty),s=T(l.onObjectEnd),F=f(l.onArrayBegin),j=T(l.onArrayEnd),w=p(l.onLiteralValue),L=O(l.onSeparator),U=T(l.onComment),E=O(l.onError),r=g&&g.disallowComments,i=g&&g.allowTrailingComma;function c(){for(;;){const k=e.scan();switch(e.getTokenError()){case 4:u(14);break;case 5:u(15);break;case 3:u(13);break;case 1:r||u(11);break;case 2:u(12);break;case 6:u(16);break}switch(k){case 12:case 13:r?u(10):U();break;case 16:u(1);break;case 15:case 14:break;default:return k}}}function u(k,o=[],a=[]){if(E(k),o.length+a.length>0){let I=e.getToken();for(;I!==17;){if(o.indexOf(I)!==-1){c();break}else if(a.indexOf(I)!==-1)break;I=c();}}}function b(k){const o=e.getTokenValue();return k?w(o):(B(o),t.push(o)),c(),!0}function m(){switch(e.getToken()){case 11:const k=e.getTokenValue();let o=Number(k);isNaN(o)&&(u(2),o=0),w(o);break;case 7:w(null);break;case 8:w(!0);break;case 9:w(!1);break;default:return !1}return c(),!0}function z(){return e.getToken()!==10?(u(3,[],[2,5]),!1):(b(!1),e.getToken()===6?(L(":"),c(),J()||u(4,[],[2,5])):u(5,[],[2,5]),t.pop(),!0)}function G(){A(),c();let k=!1;for(;e.getToken()!==2&&e.getToken()!==17;){if(e.getToken()===5){if(k||u(4,[],[]),L(","),c(),e.getToken()===2&&i)break}else k&&u(6,[],[]);z()||u(4,[],[2,5]),k=!0;}return s(),e.getToken()!==2?u(7,[2],[]):c(),!0}function M(){F(),c();let k=!0,o=!1;for(;e.getToken()!==4&&e.getToken()!==17;){if(e.getToken()===5){if(o||u(4,[],[]),L(","),c(),e.getToken()===4&&i)break}else o&&u(6,[],[]);k?(t.push(0),k=!1):t[t.length-1]++,J()||u(4,[],[4,5]),o=!0;}return j(),k||t.pop(),e.getToken()!==4?u(8,[4],[]):c(),!0}function J(){switch(e.getToken()){case 3:return M();case 1:return G();case 10:return b(!0);default:return m()}}return c(),e.getToken()===17?g.allowEmptyContent?!0:(u(4,[],[]),!1):J()?(e.getToken()!==17&&u(9,[],[]),!0):(u(4,[],[]),!1)}var Q;(function(n){n[n.None=0]="None",n[n.UnexpectedEndOfComment=1]="UnexpectedEndOfComment",n[n.UnexpectedEndOfString=2]="UnexpectedEndOfString",n[n.UnexpectedEndOfNumber=3]="UnexpectedEndOfNumber",n[n.InvalidUnicode=4]="InvalidUnicode",n[n.InvalidEscapeCharacter=5]="InvalidEscapeCharacter",n[n.InvalidCharacter=6]="InvalidCharacter";})(Q||(Q={}));var R;(function(n){n[n.OpenBraceToken=1]="OpenBraceToken",n[n.CloseBraceToken=2]="CloseBraceToken",n[n.OpenBracketToken=3]="OpenBracketToken",n[n.CloseBracketToken=4]="CloseBracketToken",n[n.CommaToken=5]="CommaToken",n[n.ColonToken=6]="ColonToken",n[n.NullKeyword=7]="NullKeyword",n[n.TrueKeyword=8]="TrueKeyword",n[n.FalseKeyword=9]="FalseKeyword",n[n.StringLiteral=10]="StringLiteral",n[n.NumericLiteral=11]="NumericLiteral",n[n.LineCommentTrivia=12]="LineCommentTrivia",n[n.BlockCommentTrivia=13]="BlockCommentTrivia",n[n.LineBreakTrivia=14]="LineBreakTrivia",n[n.Trivia=15]="Trivia",n[n.Unknown=16]="Unknown",n[n.EOF=17]="EOF";})(R||(R={}));const S=Z;var H;(function(n){n[n.InvalidSymbol=1]="InvalidSymbol",n[n.InvalidNumberFormat=2]="InvalidNumberFormat",n[n.PropertyNameExpected=3]="PropertyNameExpected",n[n.ValueExpected=4]="ValueExpected",n[n.ColonExpected=5]="ColonExpected",n[n.CommaExpected=6]="CommaExpected",n[n.CloseBraceExpected=7]="CloseBraceExpected",n[n.CloseBracketExpected=8]="CloseBracketExpected",n[n.EndOfFileExpected=9]="EndOfFileExpected",n[n.InvalidCommentToken=10]="InvalidCommentToken",n[n.UnexpectedEndOfComment=11]="UnexpectedEndOfComment",n[n.UnexpectedEndOfString=12]="UnexpectedEndOfString",n[n.UnexpectedEndOfNumber=13]="UnexpectedEndOfNumber",n[n.InvalidUnicode=14]="InvalidUnicode",n[n.InvalidEscapeCharacter=15]="InvalidEscapeCharacter",n[n.InvalidCharacter=16]="InvalidCharacter";})(H||(H={}));function P(n,l){const g=JSON.parse(n,l?.reviver);return N$1(n,g,l),g}function K(n,l){const g=S(n,l?.errors,l);return N$1(n,g,l),g}

const defaultFindOptions = {
  startingFrom: ".",
  rootPattern: /^node_modules$/,
  reverse: false,
  test: (filePath) => {
    try {
      if (statSync(filePath).isFile()) {
        return true;
      }
    } catch {
    }
  }
};
async function findFile(filename, _options = {}) {
  const filenames = Array.isArray(filename) ? filename : [filename];
  const options = { ...defaultFindOptions, ..._options };
  const basePath = resolve(options.startingFrom);
  const leadingSlash = basePath[0] === "/";
  const segments = basePath.split("/").filter(Boolean);
  if (leadingSlash) {
    segments[0] = "/" + segments[0];
  }
  let root = segments.findIndex((r) => r.match(options.rootPattern));
  if (root === -1) {
    root = 0;
  }
  if (options.reverse) {
    for (let index = root + 1; index <= segments.length; index++) {
      for (const filename2 of filenames) {
        const filePath = join(...segments.slice(0, index), filename2);
        if (await options.test(filePath)) {
          return filePath;
        }
      }
    }
  } else {
    for (let index = segments.length; index > root; index--) {
      for (const filename2 of filenames) {
        const filePath = join(...segments.slice(0, index), filename2);
        if (await options.test(filePath)) {
          return filePath;
        }
      }
    }
  }
  throw new Error(
    `Cannot find matching ${filename} in ${options.startingFrom} or parent directories`
  );
}
function findNearestFile(filename, _options = {}) {
  return findFile(filename, _options);
}
const FileCache = /* @__PURE__ */ new Map();
async function readPackageJSON(id, options = {}) {
  const resolvedPath = await resolvePackageJSON(id, options);
  const cache = options.cache && typeof options.cache !== "boolean" ? options.cache : FileCache;
  if (options.cache && cache.has(resolvedPath)) {
    return cache.get(resolvedPath);
  }
  const blob = await promises.readFile(resolvedPath, "utf8");
  let parsed;
  try {
    parsed = P(blob);
  } catch {
    parsed = K(blob);
  }
  cache.set(resolvedPath, parsed);
  return parsed;
}
async function resolvePackageJSON(id = process.cwd(), options = {}) {
  const resolvedPath = isAbsolute(id) ? id : await resolvePath(id, options);
  return findNearestFile("package.json", {
    startingFrom: resolvedPath,
    ...options
  });
}
const lockFiles = [
  "yarn.lock",
  "package-lock.json",
  "pnpm-lock.yaml",
  "npm-shrinkwrap.json",
  "bun.lockb"
];
async function resolveLockfile(id = process.cwd(), options = {}) {
  const resolvedPath = isAbsolute(id) ? id : await resolvePath(id, options);
  const _options = { startingFrom: resolvedPath, ...options };
  try {
    return await findNearestFile(lockFiles, _options);
  } catch {
  }
  throw new Error("No lockfile found from " + id);
}
async function findWorkspaceDir(id = process.cwd(), options = {}) {
  const resolvedPath = isAbsolute(id) ? id : await resolvePath(id, options);
  const _options = { startingFrom: resolvedPath, ...options };
  try {
    const r = await findNearestFile(".git/config", _options);
    return resolve(r, "../..");
  } catch {
  }
  try {
    const r = await resolveLockfile(resolvedPath, {
      ..._options,
      reverse: true
    });
    return dirname(r);
  } catch {
  }
  try {
    const r = await findFile(resolvedPath, _options);
    return dirname(r);
  } catch {
  }
  throw new Error("Cannot detect workspace root from " + id);
}

export { findWorkspaceDir as f, readPackageJSON as r };
