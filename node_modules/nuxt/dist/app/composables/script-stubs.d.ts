import type { UseScriptInput } from '@unhead/vue';
export declare function useScript<T extends Record<string | symbol, any>>(input: UseScriptInput, options?: Record<string, unknown>): void;
export declare function useScriptTriggerElement(...args: unknown[]): void;
export declare function useScriptTriggerConsent(...args: unknown[]): void;
export declare function useScriptEventPage(...args: unknown[]): void;
export declare function useScriptGoogleAnalytics(...args: unknown[]): void;
export declare function useScriptPlausibleAnalytics(...args: unknown[]): void;
export declare function useScriptCloudflareWebAnalytics(...args: unknown[]): void;
export declare function useScriptCrisp(...args: unknown[]): void;
export declare function useScriptFathomAnalytics(...args: unknown[]): void;
export declare function useScriptMatomoAnalytics(...args: unknown[]): void;
export declare function useScriptGoogleTagManager(...args: unknown[]): void;
export declare function useScriptSegment(...args: unknown[]): void;
export declare function useScriptClarity(...args: unknown[]): void;
export declare function useScriptMetaPixel(...args: unknown[]): void;
export declare function useScriptXPixel(...args: unknown[]): void;
export declare function useScriptIntercom(...args: unknown[]): void;
export declare function useScriptHotjar(...args: unknown[]): void;
export declare function useScriptStripe(...args: unknown[]): void;
export declare function useScriptLemonSqueezy(...args: unknown[]): void;
export declare function useScriptVimeoPlayer(...args: unknown[]): void;
export declare function useScriptYouTubeIframe(...args: unknown[]): void;
export declare function useScriptGoogleMaps(...args: unknown[]): void;
export declare function useScriptNpm(...args: unknown[]): void;
export declare function useScriptGoogleAdsense(...args: unknown[]): void;
export declare function useScriptYouTubePlayer(...args: unknown[]): void;
