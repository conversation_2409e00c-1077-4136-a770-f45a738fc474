import type { UseHeadInput } from '@unhead/vue';
import type { HeadAugmentations } from 'nuxt/schema';
/** @deprecated Use `UseHeadInput` from `@unhead/vue` instead. This may be removed in a future minor version. */
export type MetaObject = UseHeadInput<HeadAugmentations>;
export { 
/** @deprecated Import `useHead` from `#imports` instead. This may be removed in a future minor version. */
useHead, 
/** @deprecated Import `useSeoMeta` from `#imports` instead. This may be removed in a future minor version. */
useSeoMeta, 
/** @deprecated Import `useServerSeoMeta` from `#imports` instead. This may be removed in a future minor version. */
useServerSeoMeta, } from '@unhead/vue';
export { defineNuxtComponent } from './component.js';
export { useAsyncData, useLazyAsyncData, useNuxtData, refreshNuxtData, clearNuxtData } from './asyncData.js';
export type { AsyncDataOptions, AsyncData, AsyncDataRequestStatus } from './asyncData.js';
export { useHydration } from './hydrate.js';
export { callOnce } from './once.js';
export { useState, clearNuxtState } from './state.js';
export { clearError, createError, isNuxtError, showError, useError } from './error.js';
export type { NuxtError } from './error.js';
export { useFetch, useLazyFetch } from './fetch.js';
export type { FetchResult, UseFetchOptions } from './fetch.js';
export { useCookie, refreshCookie } from './cookie.js';
export type { CookieOptions, CookieRef } from './cookie.js';
export { onPrehydrate, prerenderRoutes, useRequestHeaders, useRequestEvent, useRequestFetch, setResponseStatus } from './ssr.js';
export { onNuxtReady } from './ready.js';
export { abortNavigation, addRouteMiddleware, defineNuxtRouteMiddleware, onBeforeRouteLeave, onBeforeRouteUpdate, setPageLayout, navigateTo, useRoute, useRouter } from './router.js';
export type { AddRouteMiddlewareOptions, RouteMiddleware } from './router.js';
export { preloadComponents, prefetchComponents, preloadRouteComponents } from './preload.js';
export { isPrerendered, loadPayload, preloadPayload, definePayloadReducer, definePayloadReviver } from './payload.js';
export { getAppManifest, getRouteRules } from './manifest.js';
export type { NuxtAppManifest, NuxtAppManifestMeta } from './manifest.js';
export type { ReloadNuxtAppOptions } from './chunk.js';
export { reloadNuxtApp } from './chunk.js';
export { useRequestURL } from './url.js';
export { usePreviewMode } from './preview.js';
export { useId } from './id.js';
export { useRouteAnnouncer } from './route-announcer.js';
