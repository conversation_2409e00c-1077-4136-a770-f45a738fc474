import { defineNuxtModule, addVitePlugin } from '@nuxt/kit';
import PluginInspect from './index.mjs';
import 'node:process';
import 'sirv';
import 'picocolors';
import 'perfect-debounce';
import 'node:path';
import 'node:url';
import 'node:buffer';
import '@rollup/pluginutils';
import 'debug';
import 'error-stack-parser-es';
import 'fs-extra';
import 'node:http';

const nuxt = defineNuxtModule({
  meta: {
    name: "vite-plugin-inspect",
    configKey: "inspect"
  },
  setup(options) {
    addVitePlugin(() => PluginInspect(options));
  }
});

export { nuxt as default };
