{"name": "@nuxt/devtools", "type": "module", "version": "1.6.0", "description": "The Nuxt DevTools gives you insights and transparency about your Nuxt App.", "license": "MIT", "homepage": "https://devtools.nuxt.com", "repository": {"type": "git", "url": "git+https://github.com/nuxt/devtools.git", "directory": "packages/devtools"}, "exports": {".": {"types": "./dist/module.d.mts", "import": "./dist/module.mjs"}, "./types": {"types": "./dist/types.d.ts"}, "./*": "./*"}, "types": "./dist/module.d.mts", "bin": "./cli.mjs", "files": ["*.cjs", "*.d.ts", "*.mjs", "dist"], "peerDependencies": {"vite": "*"}, "dependencies": {"@antfu/utils": "^0.7.10", "@nuxt/kit": "^3.13.2", "@vue/devtools-core": "7.4.4", "@vue/devtools-kit": "7.4.4", "birpc": "^0.2.17", "consola": "^3.2.3", "cronstrue": "^2.50.0", "destr": "^2.0.3", "error-stack-parser-es": "^0.1.5", "execa": "^7.2.0", "fast-npm-meta": "^0.2.2", "flatted": "^3.3.1", "get-port-please": "^3.1.2", "hookable": "^5.5.3", "image-meta": "^0.2.1", "is-installed-globally": "^1.0.0", "launch-editor": "^2.9.1", "local-pkg": "^0.5.0", "magicast": "^0.3.5", "nypm": "^0.3.11", "ohash": "^1.1.4", "pathe": "^1.1.2", "perfect-debounce": "^1.0.0", "pkg-types": "^1.2.0", "rc9": "^2.1.2", "scule": "^1.3.0", "semver": "^7.6.3", "simple-git": "^3.27.0", "sirv": "^2.0.4", "tinyglobby": "^0.2.6", "unimport": "^3.12.0", "vite-plugin-inspect": "^0.8.7", "vite-plugin-vue-inspector": "5.1.3", "which": "^3.0.1", "ws": "^8.18.0", "@nuxt/devtools-wizard": "1.6.0", "@nuxt/devtools-kit": "1.6.0"}, "devDependencies": {"@iconify-json/bxl": "^1.2.0", "@iconify-json/carbon": "^1.2.1", "@iconify-json/logos": "^1.2.0", "@iconify-json/ri": "^1.2.0", "@iconify-json/simple-icons": "^1.2.3", "@iconify-json/tabler": "^1.2.3", "@nuxt/content": "^2.13.2", "@nuxt/test-utils": "^3.14.2", "@parcel/watcher": "^2.4.1", "@types/markdown-it-link-attributes": "^3.0.5", "@types/ua-parser-js": "^0.7.39", "@unocss/nuxt": "^0.62.4", "@unocss/preset-icons": "^0.62.4", "@unocss/preset-uno": "^0.62.4", "@unocss/runtime": "^0.62.4", "@vitest/ui": "^2.1.1", "@vue/devtools-applet": "7.4.4", "@vueuse/nuxt": "^11.1.0", "@xterm/addon-fit": "^0.10.0", "@xterm/xterm": "^5.5.0", "esno": "^4.7.0", "floating-vue": "5.2.2", "fuse.js": "^7.0.0", "json-editor-vue": "^0.16.0", "markdown-it": "^14.1.0", "markdown-it-link-attributes": "^4.0.1", "nanoid": "^5.0.7", "nitropack": "^2.9.7", "nuxt": "^3.13.2", "ofetch": "^1.3.4", "quicktype-core": "^23.0.170", "shiki": "^1.18.0", "theme-vitesse": "^0.8.3", "ua-parser-js": "^1.0.39", "unocss": "^0.62.4", "unplugin-vue-markdown": "^0.26.2", "vanilla-jsoneditor": "^0.23.8", "vis-data": "^7.1.9", "vis-network": "^9.1.9", "vue-tsc": "^2.1.6", "vue-virtual-scroller": "2.0.0-beta.8", "@nuxt/devtools": "1.6.0"}, "scripts": {"build": "pnpm dev:prepare && pnpm build:module && pnpm build:client && esno scripts/prepare.ts", "build:client": "nuxi generate client", "build:module": "nuxt-build-module build", "dev": "nuxi dev client", "dev:playground": "pnpm build && nuxi dev playground", "dev:prepare": "nuxi prepare client"}}