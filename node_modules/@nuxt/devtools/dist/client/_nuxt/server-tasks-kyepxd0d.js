import{b7 as ne,D as re,b as se,b4 as ke,S as Oe,H as xe,J as Te,ai as Se,b5 as we,I as Xe,b2 as De,q as Ee,e as ue,v as Me,c as le,R as Ce,b8 as Ie,M as $e,p as Ue,W as Le}from"./d8ykqm74.js";import{_ as Ne}from"./nnavbar.vue-lvh4ek84.js";import{_ as ce}from"./nbadge-ntuzwfj6.js";import{q as Ae,k as K,b as H,l as U,Q as g,S as $,U as v,a0 as O,a5 as A,F as W,ab as Z,Y as F,u as E,W as j,r as ie,w as Re,af as fe,a8 as G,$ as I,a6 as P,O as R,a1 as Ve,aw as je,X as We,V as Be,K as He}from"./vendor/json-editor-vue-mdfcgt41.js";import{_ as pe}from"./server-route-inputs.vue-mdebn387.js";import{_ as ze}from"./ncode-block.vue-bfbbo3c9.js";import{S as de}from"./constants-b32h69zq.js";import{_ as qe}from"./nsection-block-oxvuqj5d.js";import{_ as Ye}from"./ndrawer.vue-of9w84z3.js";import"./vendor/unocss-mnpxkox6.js";import"./vendor/shiki-fpe2sz4h.js";import"./ncheckbox.vue-h2ndpucy.js";import"./nselect.vue-bpqzr4w7.js";import"./client-klf75x38.js";import"./nicon-title.vue-g9trlv29.js";var ve={exports:{}};(function(q,L){(function(x,w){q.exports=w()})(globalThis,()=>(()=>{var N={794:(b,p,_)=>{Object.defineProperty(p,"__esModule",{value:!0}),p.CronParser=void 0;var o=_(586),d=function(){function y(t,e,a){e===void 0&&(e=!0),a===void 0&&(a=!1),this.expression=t,this.dayOfWeekStartIndexZero=e,this.monthStartIndexZero=a}return y.prototype.parse=function(){var t,e,a=(t=this.expression)!==null&&t!==void 0?t:"";if(a.startsWith("@")){var r=this.parseSpecial(this.expression);e=this.extractParts(r)}else e=this.extractParts(this.expression);return this.normalize(e),this.validate(e),e},y.prototype.parseSpecial=function(t){var e={"@yearly":"0 0 1 1 *","@annually":"0 0 1 1 *","@monthly":"0 0 1 * *","@weekly":"0 0 * * 0","@daily":"0 0 * * *","@midnight":"0 0 * * *","@hourly":"0 * * * *"},a=e[t];if(!a)throw new Error("Unknown special expression.");return a},y.prototype.extractParts=function(t){if(!this.expression)throw new Error("cron expression is empty");for(var e=t.trim().split(/[ ]+/),a=0;a<e.length;a++)if(e[a].includes(",")){var r=e[a].split(",").map(function(s){return s.trim()}).filter(function(s){return s!==""}).map(function(s){return isNaN(Number(s))?s:Number(s)}).filter(function(s){return s!==null&&s!==""});r.length===0&&r.push("*"),r.sort(function(s,i){return s!==null&&i!==null?s-i:0}),e[a]=r.map(function(s){return s!==null?s.toString():""}).join(",")}if(e.length<5)throw new Error("Expression has only ".concat(e.length," part").concat(e.length==1?"":"s",". At least 5 parts are required."));if(e.length==5)e.unshift(""),e.push("");else if(e.length==6){var n=/\d{4}$/.test(e[5])||e[4]=="?"||e[2]=="?";n?e.unshift(""):e.push("")}else if(e.length>7)throw new Error("Expression has ".concat(e.length," parts; too many!"));return e},y.prototype.normalize=function(t){var e=this;if(t[3]=t[3].replace("?","*"),t[5]=t[5].replace("?","*"),t[2]=t[2].replace("?","*"),t[0].indexOf("0/")==0&&(t[0]=t[0].replace("0/","*/")),t[1].indexOf("0/")==0&&(t[1]=t[1].replace("0/","*/")),t[2].indexOf("0/")==0&&(t[2]=t[2].replace("0/","*/")),t[3].indexOf("1/")==0&&(t[3]=t[3].replace("1/","*/")),t[4].indexOf("1/")==0&&(t[4]=t[4].replace("1/","*/")),t[6].indexOf("1/")==0&&(t[6]=t[6].replace("1/","*/")),t[5]=t[5].replace(/(^\d)|([^#/\s]\d)/g,function(c){var h=c.replace(/\D/,""),f=h;return e.dayOfWeekStartIndexZero?h=="7"&&(f="0"):f=(parseInt(h)-1).toString(),c.replace(h,f)}),t[5]=="L"&&(t[5]="6"),t[3]=="?"&&(t[3]="*"),t[3].indexOf("W")>-1&&(t[3].indexOf(",")>-1||t[3].indexOf("-")>-1))throw new Error("The 'W' character can be specified only when the day-of-month is a single day, not a range or list of days.");var a={SUN:0,MON:1,TUE:2,WED:3,THU:4,FRI:5,SAT:6};for(var r in a)t[5]=t[5].replace(new RegExp(r,"gi"),a[r].toString());t[4]=t[4].replace(/(^\d{1,2})|([^#/\s]\d{1,2})/g,function(c){var h=c.replace(/\D/,""),f=h;return e.monthStartIndexZero&&(f=(parseInt(h)+1).toString()),c.replace(h,f)});var n={JAN:1,FEB:2,MAR:3,APR:4,MAY:5,JUN:6,JUL:7,AUG:8,SEP:9,OCT:10,NOV:11,DEC:12};for(var s in n)t[4]=t[4].replace(new RegExp(s,"gi"),n[s].toString());t[0]=="0"&&(t[0]=""),!/\*|\-|\,|\//.test(t[2])&&(/\*|\//.test(t[1])||/\*|\//.test(t[0]))&&(t[2]+="-".concat(t[2]));for(var i=0;i<t.length;i++)if(t[i].indexOf(",")!=-1&&(t[i]=t[i].split(",").filter(function(c){return c!==""}).join(",")||"*"),t[i]=="*/1"&&(t[i]="*"),t[i].indexOf("/")>-1&&!/^\*|\-|\,/.test(t[i])){var u=null;switch(i){case 4:u="12";break;case 5:u="6";break;case 6:u="9999";break;default:u=null;break}if(u!==null){var m=t[i].split("/");t[i]="".concat(m[0],"-").concat(u,"/").concat(m[1])}}},y.prototype.validate=function(t){this.assertNoInvalidCharacters("DOW",t[5]),this.assertNoInvalidCharacters("DOM",t[3]),this.validateRange(t)},y.prototype.validateRange=function(t){o.default.secondRange(t[0]),o.default.minuteRange(t[1]),o.default.hourRange(t[2]),o.default.dayOfMonthRange(t[3]),o.default.monthRange(t[4],this.monthStartIndexZero),o.default.dayOfWeekRange(t[5],this.dayOfWeekStartIndexZero)},y.prototype.assertNoInvalidCharacters=function(t,e){var a=e.match(/[A-KM-VX-Z]+/gi);if(a&&a.length)throw new Error("".concat(t," part contains invalid values: '").concat(a.toString(),"'"))},y}();p.CronParser=d},728:(b,p,_)=>{Object.defineProperty(p,"__esModule",{value:!0}),p.ExpressionDescriptor=void 0;var o=_(910),d=_(794),y=function(){function t(e,a){if(this.expression=e,this.options=a,this.expressionParts=new Array(5),!this.options.locale&&t.defaultLocale&&(this.options.locale=t.defaultLocale),!t.locales[this.options.locale]){var r=Object.keys(t.locales)[0];console.warn("Locale '".concat(this.options.locale,"' could not be found; falling back to '").concat(r,"'.")),this.options.locale=r}this.i18n=t.locales[this.options.locale],a.use24HourTimeFormat===void 0&&(a.use24HourTimeFormat=this.i18n.use24HourTimeFormatByDefault())}return t.toString=function(e,a){var r=a===void 0?{}:a,n=r.throwExceptionOnParseError,s=n===void 0?!0:n,i=r.verbose,u=i===void 0?!1:i,m=r.dayOfWeekStartIndexZero,c=m===void 0?!0:m,h=r.monthStartIndexZero,f=h===void 0?!1:h,C=r.use24HourTimeFormat,T=r.locale,B=T===void 0?null:T,V=r.tzOffset,D=V===void 0?0:V,z={throwExceptionOnParseError:s,verbose:u,dayOfWeekStartIndexZero:c,monthStartIndexZero:f,use24HourTimeFormat:C,locale:B,tzOffset:D},ee=new t(e,z);return ee.getFullDescription()},t.initialize=function(e,a){a===void 0&&(a="en"),t.specialCharacters=["/","-",",","*"],t.defaultLocale=a,e.load(t.locales)},t.prototype.getFullDescription=function(){var e="";try{var a=new d.CronParser(this.expression,this.options.dayOfWeekStartIndexZero,this.options.monthStartIndexZero);this.expressionParts=a.parse();var r=this.getTimeOfDayDescription(),n=this.getDayOfMonthDescription(),s=this.getMonthDescription(),i=this.getDayOfWeekDescription(),u=this.getYearDescription();e+=r+n+i+s+u,e=this.transformVerbosity(e,!!this.options.verbose),e=e.charAt(0).toLocaleUpperCase()+e.substr(1)}catch(m){if(!this.options.throwExceptionOnParseError)e=this.i18n.anErrorOccuredWhenGeneratingTheExpressionD();else throw"".concat(m)}return e},t.prototype.getTimeOfDayDescription=function(){var e=this.expressionParts[0],a=this.expressionParts[1],r=this.expressionParts[2],n="";if(!o.StringUtilities.containsAny(a,t.specialCharacters)&&!o.StringUtilities.containsAny(r,t.specialCharacters)&&!o.StringUtilities.containsAny(e,t.specialCharacters))n+=this.i18n.atSpace()+this.formatTime(r,a,e);else if(!e&&a.indexOf("-")>-1&&!(a.indexOf(",")>-1)&&!(a.indexOf("/")>-1)&&!o.StringUtilities.containsAny(r,t.specialCharacters)){var s=a.split("-");n+=o.StringUtilities.format(this.i18n.everyMinuteBetweenX0AndX1(),this.formatTime(r,s[0],""),this.formatTime(r,s[1],""))}else if(!e&&r.indexOf(",")>-1&&r.indexOf("-")==-1&&r.indexOf("/")==-1&&!o.StringUtilities.containsAny(a,t.specialCharacters)){var i=r.split(",");n+=this.i18n.at();for(var u=0;u<i.length;u++)n+=" ",n+=this.formatTime(i[u],a,""),u<i.length-2&&(n+=","),u==i.length-2&&(n+=this.i18n.spaceAnd())}else{var m=this.getSecondsDescription(),c=this.getMinutesDescription(),h=this.getHoursDescription();if(n+=m,n&&c&&(n+=", "),n+=c,c===h)return n;n&&h&&(n+=", "),n+=h}return n},t.prototype.getSecondsDescription=function(){var e=this,a=this.getSegmentDescription(this.expressionParts[0],this.i18n.everySecond(),function(r){return r},function(r){return o.StringUtilities.format(e.i18n.everyX0Seconds(r),r)},function(r){return e.i18n.secondsX0ThroughX1PastTheMinute()},function(r){return r=="0"?"":parseInt(r)<20?e.i18n.atX0SecondsPastTheMinute(r):e.i18n.atX0SecondsPastTheMinuteGt20()||e.i18n.atX0SecondsPastTheMinute(r)});return a},t.prototype.getMinutesDescription=function(){var e=this,a=this.expressionParts[0],r=this.expressionParts[2],n=this.getSegmentDescription(this.expressionParts[1],this.i18n.everyMinute(),function(s){return s},function(s){return o.StringUtilities.format(e.i18n.everyX0Minutes(s),s)},function(s){return e.i18n.minutesX0ThroughX1PastTheHour()},function(s){try{return s=="0"&&r.indexOf("/")==-1&&a==""?e.i18n.everyHour():parseInt(s)<20?e.i18n.atX0MinutesPastTheHour(s):e.i18n.atX0MinutesPastTheHourGt20()||e.i18n.atX0MinutesPastTheHour(s)}catch{return e.i18n.atX0MinutesPastTheHour(s)}});return n},t.prototype.getHoursDescription=function(){var e=this,a=this.expressionParts[2],r=this.getSegmentDescription(a,this.i18n.everyHour(),function(i){return e.formatTime(i,"0","")},function(i){return o.StringUtilities.format(e.i18n.everyX0Hours(i),i)},function(i){return e.i18n.betweenX0AndX1()},function(i){return e.i18n.atX0()});if(r&&a.includes("-")&&this.expressionParts[1]!="0"){var n=Array.from(r.matchAll(/:00/g));if(n.length>1){var s=n[n.length-1].index;r=r.substring(0,s)+":59"+r.substring(s+3)}}return r},t.prototype.getDayOfWeekDescription=function(){var e=this,a=this.i18n.daysOfTheWeek(),r=null;return this.expressionParts[5]=="*"?r="":r=this.getSegmentDescription(this.expressionParts[5],this.i18n.commaEveryDay(),function(n,s){var i=n;n.indexOf("#")>-1?i=n.substring(0,n.indexOf("#")):n.indexOf("L")>-1&&(i=i.replace("L",""));var u=parseInt(i);if(e.options.tzOffset){var m=e.expressionParts[2],c=parseInt(m)+(e.options.tzOffset?e.options.tzOffset:0);c>=24?u++:c<0&&u--,u>6?u=0:u<0&&(u=6)}var h=e.i18n.daysOfTheWeekInCase?e.i18n.daysOfTheWeekInCase(s)[u]:a[u];if(n.indexOf("#")>-1){var f=null,C=n.substring(n.indexOf("#")+1),T=n.substring(0,n.indexOf("#"));switch(C){case"1":f=e.i18n.first(T);break;case"2":f=e.i18n.second(T);break;case"3":f=e.i18n.third(T);break;case"4":f=e.i18n.fourth(T);break;case"5":f=e.i18n.fifth(T);break}h=f+" "+h}return h},function(n){return parseInt(n)==1?"":o.StringUtilities.format(e.i18n.commaEveryX0DaysOfTheWeek(n),n)},function(n){var s=n.substring(0,n.indexOf("-")),i=e.expressionParts[3]!="*";return i?e.i18n.commaAndX0ThroughX1(s):e.i18n.commaX0ThroughX1(s)},function(n){var s=null;if(n.indexOf("#")>-1){var i=n.substring(n.indexOf("#")+1);s=e.i18n.commaOnThe(i).trim()+e.i18n.spaceX0OfTheMonth()}else if(n.indexOf("L")>-1)s=e.i18n.commaOnTheLastX0OfTheMonth(n.replace("L",""));else{var u=e.expressionParts[3]!="*";s=u?e.i18n.commaAndOnX0():e.i18n.commaOnlyOnX0(n)}return s}),r},t.prototype.getMonthDescription=function(){var e=this,a=this.i18n.monthsOfTheYear(),r=this.getSegmentDescription(this.expressionParts[4],"",function(n,s){return s&&e.i18n.monthsOfTheYearInCase?e.i18n.monthsOfTheYearInCase(s)[parseInt(n)-1]:a[parseInt(n)-1]},function(n){return parseInt(n)==1?"":o.StringUtilities.format(e.i18n.commaEveryX0Months(n),n)},function(n){return e.i18n.commaMonthX0ThroughMonthX1()||e.i18n.commaX0ThroughX1()},function(n){return e.i18n.commaOnlyInMonthX0?e.i18n.commaOnlyInMonthX0():e.i18n.commaOnlyInX0()});return r},t.prototype.getDayOfMonthDescription=function(){var e=this,a=null,r=this.expressionParts[3];switch(r){case"L":a=this.i18n.commaOnTheLastDayOfTheMonth();break;case"WL":case"LW":a=this.i18n.commaOnTheLastWeekdayOfTheMonth();break;default:var n=r.match(/(\d{1,2}W)|(W\d{1,2})/);if(n){var s=parseInt(n[0].replace("W","")),i=s==1?this.i18n.firstWeekday():o.StringUtilities.format(this.i18n.weekdayNearestDayX0(),s.toString());a=o.StringUtilities.format(this.i18n.commaOnTheX0OfTheMonth(),i);break}else{var u=r.match(/L-(\d{1,2})/);if(u){var m=u[1];a=o.StringUtilities.format(this.i18n.commaDaysBeforeTheLastDayOfTheMonth(m),m);break}else{if(r=="*"&&this.expressionParts[5]!="*")return"";a=this.getSegmentDescription(r,this.i18n.commaEveryDay(),function(c){return c=="L"?e.i18n.lastDay():e.i18n.dayX0?o.StringUtilities.format(e.i18n.dayX0(),c):c},function(c){return c=="1"?e.i18n.commaEveryDay():e.i18n.commaEveryX0Days(c)},function(c){return e.i18n.commaBetweenDayX0AndX1OfTheMonth(c)},function(c){return e.i18n.commaOnDayX0OfTheMonth(c)})}break}}return a},t.prototype.getYearDescription=function(){var e=this,a=this.getSegmentDescription(this.expressionParts[6],"",function(r){return/^\d+$/.test(r)?new Date(parseInt(r),1).getFullYear().toString():r},function(r){return o.StringUtilities.format(e.i18n.commaEveryX0Years(r),r)},function(r){return e.i18n.commaYearX0ThroughYearX1()||e.i18n.commaX0ThroughX1()},function(r){return e.i18n.commaOnlyInYearX0?e.i18n.commaOnlyInYearX0():e.i18n.commaOnlyInX0()});return a},t.prototype.getSegmentDescription=function(e,a,r,n,s,i){var u=null,m=e.indexOf("/")>-1,c=e.indexOf("-")>-1,h=e.indexOf(",")>-1;if(!e)u="";else if(e==="*")u=a;else if(!m&&!c&&!h)u=o.StringUtilities.format(i(e),r(e));else if(h){for(var f=e.split(","),C="",T=0;T<f.length;T++)if(T>0&&f.length>2&&(C+=",",T<f.length-1&&(C+=" ")),T>0&&f.length>1&&(T==f.length-1||f.length==2)&&(C+="".concat(this.i18n.spaceAnd()," ")),f[T].indexOf("/")>-1||f[T].indexOf("-")>-1){var B=f[T].indexOf("-")>-1&&f[T].indexOf("/")==-1,V=this.getSegmentDescription(f[T],a,r,n,B?this.i18n.commaX0ThroughX1:s,i);B&&(V=V.replace(", ","")),C+=V}else m?C+=this.getSegmentDescription(f[T],a,r,n,s,i):C+=r(f[T]);m?u=C:u=o.StringUtilities.format(i(e),C)}else if(m){var f=e.split("/");if(u=o.StringUtilities.format(n(f[1]),f[1]),f[0].indexOf("-")>-1){var D=this.generateRangeSegmentDescription(f[0],s,r);D.indexOf(", ")!=0&&(u+=", "),u+=D}else if(f[0].indexOf("*")==-1){var z=o.StringUtilities.format(i(f[0]),r(f[0]));z=z.replace(", ",""),u+=o.StringUtilities.format(this.i18n.commaStartingX0(),z)}}else c&&(u=this.generateRangeSegmentDescription(e,s,r));return u},t.prototype.generateRangeSegmentDescription=function(e,a,r){var n="",s=e.split("-"),i=r(s[0],1),u=r(s[1],2),m=a(e);return n+=o.StringUtilities.format(m,i,u),n},t.prototype.formatTime=function(e,a,r){var n=0,s=0;this.options.tzOffset&&(n=this.options.tzOffset>0?Math.floor(this.options.tzOffset):Math.ceil(this.options.tzOffset),s=parseFloat((this.options.tzOffset%1).toFixed(2)),s!=0&&(s*=60));var i=parseInt(e)+n,u=parseInt(a)+s;u>=60?(u-=60,i+=1):u<0&&(u+=60,i-=1),i>=24?i=i-24:i<0&&(i=24+i);var m="",c=!1;this.options.use24HourTimeFormat||(c=!!(this.i18n.setPeriodBeforeTime&&this.i18n.setPeriodBeforeTime()),m=c?"".concat(this.getPeriod(i)," "):" ".concat(this.getPeriod(i)),i>12&&(i-=12),i===0&&(i=12));var h="";return r&&(h=":".concat(("00"+r).substring(r.length))),"".concat(c?m:"").concat(("00"+i.toString()).substring(i.toString().length),":").concat(("00"+u.toString()).substring(u.toString().length)).concat(h).concat(c?"":m)},t.prototype.transformVerbosity=function(e,a){return a||(e=e.replace(new RegExp(", ".concat(this.i18n.everyMinute()),"g"),""),e=e.replace(new RegExp(", ".concat(this.i18n.everyHour()),"g"),""),e=e.replace(new RegExp(this.i18n.commaEveryDay(),"g"),""),e=e.replace(/\, ?$/,"")),e},t.prototype.getPeriod=function(e){return e>=12?this.i18n.pm&&this.i18n.pm()||"PM":this.i18n.am&&this.i18n.am()||"AM"},t.locales={},t}();p.ExpressionDescriptor=y},336:(b,p,_)=>{Object.defineProperty(p,"__esModule",{value:!0}),p.enLocaleLoader=void 0;var o=_(751),d=function(){function y(){}return y.prototype.load=function(t){t.en=new o.en},y}();p.enLocaleLoader=d},751:(b,p)=>{Object.defineProperty(p,"__esModule",{value:!0}),p.en=void 0;var _=function(){function o(){}return o.prototype.atX0SecondsPastTheMinuteGt20=function(){return null},o.prototype.atX0MinutesPastTheHourGt20=function(){return null},o.prototype.commaMonthX0ThroughMonthX1=function(){return null},o.prototype.commaYearX0ThroughYearX1=function(){return null},o.prototype.use24HourTimeFormatByDefault=function(){return!1},o.prototype.anErrorOccuredWhenGeneratingTheExpressionD=function(){return"An error occured when generating the expression description.  Check the cron expression syntax."},o.prototype.everyMinute=function(){return"every minute"},o.prototype.everyHour=function(){return"every hour"},o.prototype.atSpace=function(){return"At "},o.prototype.everyMinuteBetweenX0AndX1=function(){return"Every minute between %s and %s"},o.prototype.at=function(){return"At"},o.prototype.spaceAnd=function(){return" and"},o.prototype.everySecond=function(){return"every second"},o.prototype.everyX0Seconds=function(){return"every %s seconds"},o.prototype.secondsX0ThroughX1PastTheMinute=function(){return"seconds %s through %s past the minute"},o.prototype.atX0SecondsPastTheMinute=function(){return"at %s seconds past the minute"},o.prototype.everyX0Minutes=function(){return"every %s minutes"},o.prototype.minutesX0ThroughX1PastTheHour=function(){return"minutes %s through %s past the hour"},o.prototype.atX0MinutesPastTheHour=function(){return"at %s minutes past the hour"},o.prototype.everyX0Hours=function(){return"every %s hours"},o.prototype.betweenX0AndX1=function(){return"between %s and %s"},o.prototype.atX0=function(){return"at %s"},o.prototype.commaEveryDay=function(){return", every day"},o.prototype.commaEveryX0DaysOfTheWeek=function(){return", every %s days of the week"},o.prototype.commaX0ThroughX1=function(){return", %s through %s"},o.prototype.commaAndX0ThroughX1=function(){return", %s through %s"},o.prototype.first=function(){return"first"},o.prototype.second=function(){return"second"},o.prototype.third=function(){return"third"},o.prototype.fourth=function(){return"fourth"},o.prototype.fifth=function(){return"fifth"},o.prototype.commaOnThe=function(){return", on the "},o.prototype.spaceX0OfTheMonth=function(){return" %s of the month"},o.prototype.lastDay=function(){return"the last day"},o.prototype.commaOnTheLastX0OfTheMonth=function(){return", on the last %s of the month"},o.prototype.commaOnlyOnX0=function(){return", only on %s"},o.prototype.commaAndOnX0=function(){return", and on %s"},o.prototype.commaEveryX0Months=function(){return", every %s months"},o.prototype.commaOnlyInX0=function(){return", only in %s"},o.prototype.commaOnTheLastDayOfTheMonth=function(){return", on the last day of the month"},o.prototype.commaOnTheLastWeekdayOfTheMonth=function(){return", on the last weekday of the month"},o.prototype.commaDaysBeforeTheLastDayOfTheMonth=function(){return", %s days before the last day of the month"},o.prototype.firstWeekday=function(){return"first weekday"},o.prototype.weekdayNearestDayX0=function(){return"weekday nearest day %s"},o.prototype.commaOnTheX0OfTheMonth=function(){return", on the %s of the month"},o.prototype.commaEveryX0Days=function(){return", every %s days"},o.prototype.commaBetweenDayX0AndX1OfTheMonth=function(){return", between day %s and %s of the month"},o.prototype.commaOnDayX0OfTheMonth=function(){return", on day %s of the month"},o.prototype.commaEveryHour=function(){return", every hour"},o.prototype.commaEveryX0Years=function(){return", every %s years"},o.prototype.commaStartingX0=function(){return", starting %s"},o.prototype.daysOfTheWeek=function(){return["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},o.prototype.monthsOfTheYear=function(){return["January","February","March","April","May","June","July","August","September","October","November","December"]},o}();p.en=_},586:(b,p)=>{Object.defineProperty(p,"__esModule",{value:!0});function _(d,y){if(!d)throw new Error(y)}var o=function(){function d(){}return d.secondRange=function(y){for(var t=y.split(","),e=0;e<t.length;e++)if(!isNaN(parseInt(t[e],10))){var a=parseInt(t[e],10);_(a>=0&&a<=59,"seconds part must be >= 0 and <= 59")}},d.minuteRange=function(y){for(var t=y.split(","),e=0;e<t.length;e++)if(!isNaN(parseInt(t[e],10))){var a=parseInt(t[e],10);_(a>=0&&a<=59,"minutes part must be >= 0 and <= 59")}},d.hourRange=function(y){for(var t=y.split(","),e=0;e<t.length;e++)if(!isNaN(parseInt(t[e],10))){var a=parseInt(t[e],10);_(a>=0&&a<=23,"hours part must be >= 0 and <= 23")}},d.dayOfMonthRange=function(y){for(var t=y.split(","),e=0;e<t.length;e++)if(!isNaN(parseInt(t[e],10))){var a=parseInt(t[e],10);_(a>=1&&a<=31,"DOM part must be >= 1 and <= 31")}},d.monthRange=function(y,t){for(var e=y.split(","),a=0;a<e.length;a++)if(!isNaN(parseInt(e[a],10))){var r=parseInt(e[a],10);_(r>=1&&r<=12,t?"month part must be >= 0 and <= 11":"month part must be >= 1 and <= 12")}},d.dayOfWeekRange=function(y,t){for(var e=y.split(","),a=0;a<e.length;a++)if(!isNaN(parseInt(e[a],10))){var r=parseInt(e[a],10);_(r>=0&&r<=6,t?"DOW part must be >= 0 and <= 6":"DOW part must be >= 1 and <= 7")}},d}();p.default=o},910:(b,p)=>{Object.defineProperty(p,"__esModule",{value:!0}),p.StringUtilities=void 0;var _=function(){function o(){}return o.format=function(d){for(var y=[],t=1;t<arguments.length;t++)y[t-1]=arguments[t];return d.replace(/%s/g,function(e){return y.shift()})},o.containsAny=function(d,y){return y.some(function(t){return d.indexOf(t)>-1})},o}();p.StringUtilities=_}},x={};function w(b){var p=x[b];if(p!==void 0)return p.exports;var _=x[b]={exports:{}};return N[b](_,_.exports,w),_.exports}var X={};return(()=>{var b=X;Object.defineProperty(b,"__esModule",{value:!0}),b.toString=void 0;var p=w(728),_=w(336);p.ExpressionDescriptor.initialize(new _.enLocaleLoader),b.default=p.ExpressionDescriptor;var o=p.ExpressionDescriptor.toString;b.toString=o})(),X})())})(ve);var Pe=ve.exports;const Fe=Ae(Pe),Ge=["title"],Ze={"flex-none":"","text-left":""},Je={flex:"~ gap-2","min-w-0":"","items-center":"","text-sm":""},Qe={class:"truncate"},Ke={"text-xs":"",op50:""},et={key:0},tt=["onClick"],nt=K({__name:"CronCollection",props:{collection:{}},setup(q){const L=q,N=ne(),x=H(!0),w=U(()=>Fe.toString(L.collection.cron));return(X,b)=>{const p=re,_=ce;return g(),$("div",null,[v("button",{flex:"~ gap-2","w-full":"","items-start":"","items-center":"","hover-bg-active":"",p2:"",title:w.value,onClick:b[0]||(b[0]=o=>x.value=!x.value)},[v("div",Ze,[O(p,{icon:"carbon:chevron-right","mb0.5":"","transform-rotate":x.value?90:0,transition:""},null,8,["transform-rotate"])]),v("span",Je,[O(_,{class:"font-mono n-primary",textContent:A(X.collection.cron)},null,8,["textContent"]),v("span",Qe,A(w.value),1),v("span",Ke,"("+A(X.collection.tasks.length)+")",1)])],8,Ge),b[2]||(b[2]=v("div",{"x-divider":""},null,-1)),x.value?(g(),$("ul",et,[(g(!0),$(W,null,Z(X.collection.tasks,o=>(g(),$("li",{key:o},[v("button",{flex:"~ gap-2","w-full":"","items-start":"","items-center":"","hover-bg-active":"",px2:"",py1:"","pl-9":"","font-mono":"",class:F([{"bg-active":E(N)===o}]),onClick:d=>N.value=o},A(o),11,tt),b[1]||(b[1]=v("div",{"x-divider":""},null,-1))]))),128))])):j("",!0)])}}}),rt={"h-full":"","w-full":"",flex:"~ col"},ot={flex:"~ col gap-2","flex-none":"",p4:"","n-navbar-glass":""},at={flex:"~ gap2 items-center"},it={relative:"","w-full":""},st={absolute:"","right-2":"","top-1.5":"",flex:"~ gap-1"},ut={flex:"~ gap2 wrap","w-full":"","items-center":"",px4:"",pb2:"","text-center":"","text-sm":"",border:"b base"},lt={class:"hidden md:block"},ct={flex:"~ gap2","mb--2":"","items-center":"",op50:""},ft={key:0,border:"b base",relative:"","n-code-block":""},pt={flex:"~ wrap","w-full":""},dt=["onClick"],vt={border:"b base",flex:"~ gap2","items-center":"",px4:"",py2:""},ht={key:1,"text-xs":"",op50:""},mt=K({__name:"ServerTaskDetails",props:{task:{}},emits:["open-default-input"],setup(q,{emit:L}){const N=q,x=L,w=H({payload:{}}),{inputDefaults:X}=se("serverRoutes"),[b,p]=ke(),_=Oe(),o=xe(),d=ie({contentType:"text/plain",data:"",statusCode:200,error:void 0,fetchTime:0}),y=U(()=>JSON.stringify(d.data,null,2)),t=U(()=>d.contentType.includes("application/json")?"json":"text"),e=H(!1),a=H(!1),r=Te(),n=H(),s=["json"],i=H(s[0]),u=ie({query:[{active:!0,key:"",value:"",type:"string"}],body:[{active:!0,key:"",value:"",type:"string"}],headers:[]}),m=U({get:()=>u[n.value],set:k=>{u[n.value]=k}}),c=U(()=>({...D(X.value.query),...D(u.query)})),h=U(()=>({...D(X.value.headers)})),f=U(()=>Object.keys(w.value?.payload??{}).length?{...w.value}:void 0),C=U(()=>f.value?"POST":"GET"),T=U(()=>{let k=window?.location.origin;return k.charAt(k.length-1)==="/"&&(k=k.slice(0,-1)),k}),B=U(()=>{let k=new URLSearchParams(c.value).toString();k&&(k=`?${k}`);const l=`/_nitro/tasks/${N.task.name}${k}`;let M=o.value?.app.baseURL||"";return(M==="./"||M===".")&&(M=""),M.endsWith("/")&&(M=M.slice(0,-1)),M+l}),V=U(()=>T.value+B.value);function D(k){const l=Object.fromEntries(k.filter(({active:M,key:Y,value:J})=>M&&Y&&J!==void 0).map(({key:M,value:Y})=>[M,Y]));return Object.entries(l).length?l:void 0}async function z(){a.value=!0,e.value=!0;const k=Date.now();Ee("server-tasks:run",{method:C.value});try{d.data=await $fetch(V.value,{method:"POST",headers:h.value,query:c.value,body:f.value,onResponse({response:l}){d.contentType=(l.headers.get("content-type")||"").toString().toLowerCase().trim(),d.statusCode=l.status,d.error=void 0},onResponseError(l){d.error=l.response._data,d.data=l.response._data}})}catch{}e.value=!1,d.fetchTime=Date.now()-k}const ee=U(()=>{const k=[];return k.push({name:"Query",slug:"query",length:u.query.length}),k.push({name:"Body",slug:"body",length:u.body.length}),k});Re(()=>{if(i.value==="json"&&typeof w.value=="string")try{w.value=JSON.parse(w.value)}catch{}});const oe=Se("nuxt-devtools:server-tasks:inputs",()=>[],{window:window.parent});we([u,n],()=>{const k=oe.value?.find(l=>l.task===N.task.name);if(k){n.value||(n.value=k.tab),k.tab!==n.value&&(k.tab=n.value);const{body:l,query:M,headers:Y}=k.inputs;Object.assign(u,{body:l,query:M,headers:Y})}else{const l={task:N.task.name,tab:"query",inputs:u};oe.value.push(l),n.value||(n.value=l.tab)}},{immediate:!0,deep:!0,debounce:500});const me=Xe();return(k,l)=>{const M=ue,Y=Me,J=re,ae=pe,ye=le,ge=Ce,te=ce,_e=ze,Q=fe("tooltip");return g(),$("div",rt,[v("div",ot,[v("div",at,[G((g(),$("div",null,[O(M,{class:F(["n-badge-base n-sm",E(De)(C.value)]),"pointer-events-none":"","font-mono":"",tabindex:"-1"},{default:I(()=>[P(A(C.value),1)]),_:1},8,["class"])])),[[Q,`Method is ${C.value} as ${C.value==="GET"?"no":""} json body is sent`]]),v("div",it,[O(Y,{"model-value":B.value,readonly:"","flex-auto":"","font-mono":"",p:"x5 y2",n:"sm"},null,8,["model-value"]),v("div",st,[G(O(M,{title:"Copy URL",n:"xs blue",icon:"carbon:copy",border:!1,onClick:l[0]||(l[0]=S=>E(me)(V.value,"server-task-url"))},null,512),[[Q,"Copy URL"]]),G(O(M,{title:"Open in Editor",icon:"carbon-launch",n:"xs blue",border:!1,onClick:l[1]||(l[1]=S=>E(r)(k.task.handler))},null,512),[[Q,"Open in Editor"]])])]),O(M,{"h-full":"",n:"primary solid",onClick:z},{default:I(()=>[O(J,{icon:"carbon:send"})]),_:1})])]),v("div",ut,[(g(!0),$(W,null,Z(ee.value,S=>G((g(),R(M,{key:S.slug,class:F(n.value===S.slug?"text-primary n-primary":"border-transparent shadow-none"),onClick:be=>n.value=S.slug},{default:I(()=>[O(J,{icon:E(de)[S.slug]},null,8,["icon"]),v("div",lt,[P(A(S.name)+" "+A(S?.length?`(${S.length})`:"")+" ",1),v("span",null,A(E(X)[S.slug]?.length?`(${E(X)[S.slug].length})`:""),1)])]),_:2},1032,["class","onClick"])),[[Q,S.name]])),128))]),O(E(b),null,{default:I(()=>[O(ae,{modelValue:m.value,"onUpdate:modelValue":l[4]||(l[4]=S=>m.value=S),default:{active:!0,type:"string"},"max-h-xs":"","of-auto":""},{default:I(()=>[E(X)[n.value]?.length?(g(),$(W,{key:0},[v("div",ct,[l[6]||(l[6]=v("div",{"w-5":"","x-divider":""},null,-1)),l[7]||(l[7]=v("div",{"flex-none":""}," Default Inputs ",-1)),O(M,{icon:"i-carbon-edit",border:!1,onClick:l[2]||(l[2]=S=>x("open-default-input"))}),l[8]||(l[8]=v("div",{"x-divider":""},null,-1))]),O(ae,{modelValue:E(X)[n.value],"onUpdate:modelValue":l[3]||(l[3]=S=>E(X)[n.value]=S),disabled:"",p0:""},null,8,["modelValue"])],64)):j("",!0)]),_:1},8,["modelValue"])]),_:1}),m.value?(g(),$("div",ft,[n.value==="body"?(g(),$(W,{key:0},[v("div",pt,[(g(),$(W,null,Z(s,S=>v("button",{key:S,px4:"",py2:"",border:"r base",hover:"bg-active",class:F({"border-b":S!==i.value}),onClick:be=>i.value=S},[v("div",{class:F({op30:S!==i.value}),"font-mono":""},A(S),3)],10,dt)),64)),l[9]||(l[9]=v("div",{border:"b base","flex-auto":""},null,-1))]),i.value==="input"?(g(),R(E(p),{key:0})):i.value==="json"?(g(),R(E(je),Ve({key:1,modelValue:w.value,"onUpdate:modelValue":l[5]||(l[5]=S=>w.value=S),class:[E(_)==="dark"?"jse-theme-dark":"light","json-editor-vue of-auto text-sm outline-none"]},k.$attrs,{mode:"text","navigation-bar":!1,indentation:2,"tab-size":2}),null,16,["modelValue","class"])):j("",!0)],64)):(g(),R(E(p),{key:1}))])):j("",!0),a.value?e.value?(g(),R(ge,{key:2,"z-10":"","flex-auto":"","backdrop-blur":""},{default:I(()=>l[11]||(l[11]=[P(" Running... ")])),_:1})):(g(),$(W,{key:3},[v("div",vt,[l[13]||(l[13]=v("div",null,"Result",-1)),d.error?(g(),R(te,{key:0,n:"red"},{default:I(()=>l[12]||(l[12]=[P(" Error ")])),_:1})):j("",!0),O(te,{n:d.error?"orange":"green",textContent:A(d.statusCode)},null,8,["n","textContent"]),d.contentType?(g(),$("code",ht,A(d.contentType),1)):j("",!0),l[14]||(l[14]=v("div",{"flex-auto":""},null,-1)),l[15]||(l[15]=v("div",{op50:""}," Tasks finished in ",-1)),O(te,{n:"green"},{default:I(()=>[P(A(d.fetchTime)+" ms ",1)]),_:1})]),O(_e,{"flex-auto":"","overflow-auto":"","py-2":"",code:y.value,lang:t.value},null,8,["code","lang"])],64)):(g(),R(ye,{key:1},{default:I(()=>[O(M,{n:"primary",onClick:z},{default:I(()=>[O(J,{icon:"carbon:send"}),l[10]||(l[10]=P(" Run task "))]),_:1})]),_:1}))])}}}),yt={"flex-none":"","text-left":""},gt={flex:"","items-center":"","text-sm":"","font-mono":""},he=K({__name:"ServerTaskListItem",props:{item:{},index:{default:0}},setup(q){const L=H(!0),N=ne();return(x,w)=>{const X=re,b=he;return g(),$("div",null,[v("button",{flex:"~ gap-2","w-full":"","items-start":"","items-center":"","hover-bg-active":"",px2:"",py1:"",class:F([{"bg-active":E(N)===x.item.name}]),style:We({paddingLeft:`calc(0.5rem + ${x.index*1.5}em)`}),onClick:w[0]||(w[0]=p=>{L.value=!L.value,N.value=x.item.name})},[v("div",yt,[x.item.type==="collection"?(g(),R(X,{key:0,icon:"carbon:chevron-right","mb0.5":"","transform-rotate":L.value?90:0,transition:""},null,8,["transform-rotate"])):j("",!0)]),v("span",gt,[x.item.type==="collection"?(g(),R(X,{key:0,title:`${x.item.tasks?.length} tasks`,icon:"carbon:folder",mr1:""},null,8,["title"])):(g(),R(X,{key:1,icon:"carbon:play",ml3:"",mr1:""})),P(" "+A(x.item.name),1)])],6),w[1]||(w[1]=v("div",{"x-divider":""},null,-1)),L.value?Be(x.$slots,"default",{key:0},()=>[(g(!0),$(W,null,Z(x.item.tasks,p=>(g(),R(b,{key:p.name,item:p,index:x.index+1},null,8,["item","index"]))),128))]):j("",!0)])}}}),_t={flex:"~ gap1","text-sm":""},bt={key:0,op50:""},kt={op50:""},At=K({__name:"server-tasks",setup(q){const L=H(!1),N=Ie(),x=U(()=>Object.keys(N.value?.tasks??{}).map(r=>({name:r,...N.value.tasks[r],type:"task"}))),w=U(()=>Object.entries(N.value?.scheduledTasks??{}).map(([r,n])=>({cron:r,tasks:n}))),X=ne(),{view:b,selectedTask:p,inputDefaults:_}=se("serverTasks"),o=U(()=>{!X.value&&p.value&&(X.value=p.value.name);const r=x.value.find(n=>n.name===X.value);return X.value!==p.value?.name&&r&&(p.value=r),r}),d=H(""),y=U(()=>new $e(x.value,{keys:["key","description"],shouldSort:!0})),t=U(()=>d.value?y.value.search(d.value).map(n=>n.item):x.value),e=U(()=>{const r=[],n=(i,u)=>{i.tasks||=[],i.tasks.push(u)},s=(i,u)=>{const m=u?u.tasks?.find(h=>h.name===i):r.find(h=>h.name===i);if(m)return{...m,type:"collection"};const c={name:i,handler:i,description:"",type:"collection",tasks:[]};return u?n(u,c):r.push(c),c};return t.value.forEach(i=>{let u;const m={...i,type:"task"},h=i.name.split(":").concat();h.length>0&&h[h.length-1].includes(".")&&h.pop(),h.forEach(f=>{u=s(f,u)}),u?n(u,m):r.push(m)}),r});function a(){b.value=b.value==="tree"?"list":"tree"}return(r,n)=>{const s=ue,i=Ne,u=nt,m=Ue,c=mt,h=Le,f=le,C=pe,T=qe,B=Ye,V=fe("tooltip");return g(),$(W,null,[O(m,{"storage-key":"tab-server-tasks"},{left:I(()=>[O(m,{horizontal:"","storage-key":"tab-server-tasks-split"},{left:I(()=>[O(i,{search:d.value,"onUpdate:search":n[1]||(n[1]=D=>d.value=D),pb2:""},{actions:I(()=>[G(O(s,{"text-lg":"",icon:E(b)==="list"?"i-carbon-list":"i-carbon-tree-view-alt",title:"Toggle view",border:!1,onClick:a},null,8,["icon"]),[[V,"Toggle View"]]),G(O(s,{"text-lg":"",icon:"i-carbon-cics-sit-overrides",title:"Default Inputs",border:!1,onClick:n[0]||(n[0]=D=>L.value=!L.value)},null,512),[[V,"Default Inputs"]])]),default:I(()=>[v("div",_t,[d.value?(g(),$("span",bt,A(t.value.length)+" matched · ",1)):j("",!0),v("span",kt,A(x.value?.length)+" tasks in total",1)])]),_:1},8,["search"]),(g(!0),$(W,null,Z(E(b)==="tree"?e.value:t.value,D=>(g(),R(he,{key:D.name,item:D},null,8,["item"]))),128))]),right:I(()=>[n[6]||(n[6]=v("div",{px4:"",py2:"",border:"b base"}," Cron groups ",-1)),v("div",null,[v("ul",null,[(g(!0),$(W,null,Z(w.value,D=>(g(),$("li",{key:D.cron},[O(u,{collection:D},null,8,["collection"])]))),128))])])]),_:1})]),right:I(()=>[(g(),R(He,{max:10},[o.value?(g(),R(c,{key:o.value.name,task:o.value,onOpenDefaultInput:n[2]||(n[2]=D=>L.value=!0)},null,8,["task"])):j("",!0)],1024)),o.value?j("",!0):(g(),R(f,{key:0},{default:I(()=>[O(h,{px6:"",py2:""},{default:I(()=>n[7]||(n[7]=[v("span",{op75:""},"Select a task to start",-1)])),_:1})]),_:1}))]),_:1}),O(B,{modelValue:L.value,"onUpdate:modelValue":n[4]||(n[4]=D=>L.value=D),"auto-close":"","max-w-xl":"","min-w-xl":"",onClose:n[5]||(n[5]=D=>L.value=!1)},{default:I(()=>[v("div",null,[n[8]||(n[8]=v("div",{p4:"",border:"b base"},[v("span",{"text-lg":""},"Default Inputs"),v("br"),v("span",{"text-white":"",op50:""},"Merged as default for every task in DevTools")],-1)),O(T,{text:`Query ${E(_).query.length?`(${E(_).query.length})`:""}`,padding:!1,icon:E(de).query},{default:I(()=>[O(C,{modelValue:E(_).query,"onUpdate:modelValue":n[3]||(n[3]=D=>E(_).query=D),py0:"",default:{active:!0,type:"string"}},null,8,["modelValue"])]),_:1},8,["text","icon"])])]),_:1},8,["modelValue"])],64)}}});export{At as default};
