import{_ as V}from"./nnavbar.vue-lvh4ek84.js";import{_ as D}from"./ncode-block.vue-bfbbo3c9.js";import{bh as S,bi as B,M as j,p as P,W as U,c as W}from"./d8ykqm74.js";import{Q as i,S as c,U as a,k as E,b as f,w as I,l as _,a0 as l,$ as u,F as d,ab as z,a5 as v,Y as G,O as H}from"./vendor/json-editor-vue-mdfcgt41.js";import{_ as L}from"./help-fab.vue-fdt1wizc.js";import"./client-klf75x38.js";import"./vendor/unocss-mnpxkox6.js";import"./vendor/shiki-fpe2sz4h.js";const M={class:"markdown-body"},O={__name:"virtual-files",setup(h,{expose:r}){return r({frontmatter:{}}),(p,o)=>(i(),c("div",M,o[0]||(o[0]=[a("h1",null,"Virtual Files",-1),a("p",null,"Virtual files are generated on the fly to support the conventions of the framework, and to provide a better developer experience.",-1)])))}},Q=O,R=["onClick"],Y={key:0,"h-full":"","of-hidden":"",flex:"~ col"},q={border:"b base","flex-none":"",px4:"",py2:"","text-sm":"",op75:""},ne=E({__name:"virtual-files",setup(h){const r=f(""),n=S(),p=B(),o=f();I(()=>{if(!p.value)return;const e=`/_vfs.json/${encodeURIComponent(p.value)}`;fetch(e,{headers:{accept:"application/json"}}).then(t=>t.json()).then(t=>o.value=t.current)});function x(e){if(n.value?.rootDir)return e.startsWith(n.value?.rootDir)?e.slice(n.value.rootDir.length):e}const m=_(()=>n.value?n.value.entries.filter(e=>!e.id.startsWith(`${n.value?.rootDir||""}/.nuxt/`)).sort((e,t)=>e.id.localeCompare(t.id)):[]),b=_(()=>new j(m.value,{keys:["id","path"]})),k=_(()=>r.value?b.value.search(r.value).map(e=>e.item):m.value);return(e,t)=>{const y=V,g=D,C=U,$=W,F=P,N=Q,w=L;return i(),c(d,null,[l(F,{class:"virtual-files","storage-key":"tab-virtual-files"},{left:u(()=>[l(y,{search:r.value,"onUpdate:search":t[0]||(t[0]=s=>r.value=s),"no-padding":"",p3:""},null,8,["search"]),(i(!0),c(d,null,z(k.value,s=>(i(),c(d,{key:s.id},[a("button",{block:"","w-full":"","select-none":"",truncate:"",px2:"",py1:"","text-start":"","text-sm":"","font-mono":"",class:G(s.id===o.value?.id?"text-primary n-bg-active":"text-secondary hover:n-bg-hover"),onClick:A=>p.value=s.id},v(x(s.id)),11,R),t[1]||(t[1]=a("div",{"x-divider":""},null,-1))],64))),128))]),right:u(()=>[o.value?.content?(i(),c("div",Y,[a("div",q,[a("code",null,v(o.value.id),1)]),l(g,{"h-full":"","of-auto":"","text-sm":"",code:o.value.content,lang:"typescript"},null,8,["code"])])):(i(),H($,{key:1},{default:u(()=>[l(C,{px6:"",py2:""},{default:u(()=>t[2]||(t[2]=[a("span",{op75:""},"Select a file to start",-1)])),_:1})]),_:1}))]),_:1}),l(w,null,{default:u(()=>[l(N)]),_:1})],64)}}});export{ne as default};
