import{_ as O}from"./ncheckbox.vue-h2ndpucy.js";import{y as j,v as R,D as T,e as A}from"./d8ykqm74.js";import{_ as E}from"./nselect.vue-bpqzr4w7.js";import{k as L,l as N,c as z,Q as s,S as u,F as c,ab as h,V as b,O as p,W as y,Y as v,a0 as m,$ as V,U as C,a5 as K,u as r,a6 as S}from"./vendor/json-editor-vue-mdfcgt41.js";const M={p4:"",flex:"~ col gap-4"},Q={key:1,ml2:"",flex:""},W=["value"],Y={key:0,flex:"","gap-4":""},X=L({__name:"ServerRouteInputs",props:{modelValue:{},keys:{default:()=>[]},default:{default:()=>({})},disabled:{type:Boolean,default:!1}},setup(U,{emit:w}){const g=U,d=j(g,"modelValue",w,{passive:!0}),_=N(()=>[...g.keys,"active","key","value","type"]),B=N(()=>{const l={};for(const e of _.value)l[e]=g.default[e]||"";return l}),D=["string","number","boolean","file","date","time","datetime-local"];function F(l,e){const t=e.target;if(t.files&&t.files[0]){const i=t.files[0],f=new FileReader;f.readAsDataURL(i),f.onload=()=>{d.value[l].value=f.result}}}return z(()=>d,l=>{l.value.forEach(e=>{if(e.type==="number"&&typeof e.value!="number"){const t=Number.parseFloat(e.value);e.value=Number.isNaN(t)?0:t}else e.type==="boolean"&&typeof e.value!="boolean"?e.value=!0:e.type==="file"&&typeof e.value!="object"?e.value="":e.type==="date"&&typeof e.value=="string"&&!e.value.match(/^\d{4}-\d{2}-\d{2}$/)?e.value=new Date().toISOString().slice(0,10):e.type==="time"&&typeof e.value=="string"&&!e.value.match(/^\d{2}:\d{2}$/)?e.value=new Date().toISOString().slice(11,16):e.type==="datetime-local"&&typeof e.value=="string"&&!e.value.match(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}$/)?e.value=new Date().toISOString().slice(0,16):e.type==="string"&&(e.value=e.value.toString())})},{deep:!0,immediate:!0,flush:"sync"}),(l,e)=>{const t=O,i=R,f=E,I=T,k=A;return s(),u("div",M,[(s(!0),u(c,null,h(r(d),(a,$)=>(s(),u("div",{key:$,flex:"~ gap-2","justify-around":""},[b(l.$slots,"input",{item:a}),(s(!0),u(c,null,h(_.value,n=>(s(),u(c,{key:n},[a.type!==null&&n==="active"?(s(),p(t,{key:0,modelValue:a[n],"onUpdate:modelValue":o=>a[n]=o,n:"sm primary",disabled:l.disabled},null,8,["modelValue","onUpdate:modelValue","disabled"])):y("",!0),a.type!==null&&n==="key"?(s(),p(i,{key:1,modelValue:a[n],"onUpdate:modelValue":o=>a[n]=o,placeholder:n,"flex-1":"","font-mono":"",n:"sm primary",disabled:l.disabled,class:v(l.disabled?"op50":"")},null,8,["modelValue","onUpdate:modelValue","placeholder","disabled","class"])):n==="value"?(s(),u(c,{key:2},[a.type==="file"?(s(),p(i,{key:0,type:"file",disabled:l.disabled,class:v(l.disabled?"op75":""),onChange:o=>F($,o)},null,8,["disabled","class","onChange"])):a.type==="boolean"?(s(),u("div",Q,[m(t,{modelValue:a.value,"onUpdate:modelValue":o=>a.value=o,placeholder:"Value",n:"green lg",disabled:l.disabled},null,8,["modelValue","onUpdate:modelValue","disabled"])])):(s(),p(i,{key:2,modelValue:a.value,"onUpdate:modelValue":o=>a.value=o,type:a.type,placeholder:"Value","flex-1":"","font-mono":"",n:"sm primary",disabled:l.disabled,class:v(l.disabled?"op75":"")},null,8,["modelValue","onUpdate:modelValue","type","disabled","class"]))],64)):n==="type"?(s(),p(f,{key:3,modelValue:a.type,"onUpdate:modelValue":o=>a.type=o,n:"sm green",class:v(l.disabled?"op75":""),disabled:l.disabled},{default:V(()=>[(s(),u(c,null,h(D,o=>C("option",{key:o,value:o},K(o),9,W)),64))]),_:2},1032,["modelValue","onUpdate:modelValue","class","disabled"])):y("",!0)],64))),128)),b(l.$slots,"input-actions",{},()=>[m(k,{n:"red",disabled:l.disabled,class:v(l.disabled?"op0!":""),onClick:n=>r(d).splice($,1)},{default:V(()=>[m(I,{icon:"carbon:trash-can"})]),_:2},1032,["disabled","class","onClick"])])]))),128)),l.disabled?y("",!0):(s(),u("div",Y,[b(l.$slots,"actions",{params:r(d)},()=>[m(k,{icon:"carbon-add",n:"sm primary",my1:"","px-3":"",onClick:e[0]||(e[0]=a=>r(d).push({...B.value}))},{default:V(()=>e[2]||(e[2]=[S(" Add ")])),_:1}),e[4]||(e[4]=C("div",{"flex-auto":""},null,-1)),r(d).length?(s(),p(k,{key:0,icon:"carbon-trash-can",n:"sm red",my1:"","px-3":"",onClick:e[1]||(e[1]=a=>r(d).splice(0,r(d).length))},{default:V(()=>e[3]||(e[3]=[S(" Remove All ")])),_:1})):y("",!0)])])),b(l.$slots,"default")])}}});export{X as _};
