import{b9 as B,z as E,r as f,h as p,e as $,bb as V,n as D,D as F,a5 as z}from"./d8ykqm74.js";import{x as I,a as M}from"./vendor/xterm-h0ftnevm.js";import{k as y,b as g,B as P,Q as t,S as d,U as c,a0 as A,O as m,W as v,a5 as _,F as N,l as R,w as L,u as x,ab as S,Y as h,ah as j,a6 as C,$ as q}from"./vendor/json-editor-vue-mdfcgt41.js";import"./vendor/unocss-mnpxkox6.js";import"./vendor/shiki-fpe2sz4h.js";const O={border:"t base",flex:"~ gap-2","items-center":"",p2:""},Q={"text-sm":"",op50:""},U=y({__name:"TerminalView",props:{id:{}},setup(b){const n=b,i=g(),r=B(),o=g();let a;P(async()=>{a=new I.Terminal({convertEol:!0,cols:80,screenReaderMode:!0});const u=new M.FitAddon;a.loadAddon(u),a.open(i.value),u.fit(),E(window,"resize",()=>{u.fit()}),o.value=await f.getTerminalDetail(await p(),n.id),o.value?.buffer&&a.write(o.value.buffer),r.hook("devtools:terminal:data",({id:e,data:l})=>{e===n.id&&a.write(l)})});async function s(){f.runTerminalAction(await p(),n.id,"clear"),a?.clear()}async function k(){f.runTerminalAction(await p(),n.id,"restart")}async function w(){f.runTerminalAction(await p(),n.id,"terminate")}return(u,e)=>{const l=$;return t(),d(N,null,[c("div",{ref_key:"container",ref:i,"h-full":"","w-full":"","of-auto":"","bg-black":""},null,512),c("div",O,[A(l,{title:"Clear",icon:"i-carbon-clean",border:!1,onClick:e[0]||(e[0]=T=>s())}),o.value?.restartable?(t(),m(l,{key:0,title:"Restart",icon:"carbon-renew",border:!1,onClick:e[1]||(e[1]=T=>k())})):v("",!0),o.value?.terminatable?(t(),m(l,{key:1,title:"Terminate",icon:"carbon-delete",border:!1,onClick:e[2]||(e[2]=T=>w())})):v("",!0),c("span",Q,_(o.value?.description),1)])],64)}}}),W={key:0,"h-full":"","w-full":"","of-hidden":"",grid:"~ rows-[max-content_1fr_max-content]"},Y={flex:"~",border:"b base","flex-1":"","items-center":"","n-navbar-glass":""},G=["onClick"],H={key:1,p10:""},J={key:1,"h-full":"",flex:"","items-center":"","justify-center":""},K=y({__name:"TerminalPage",setup(b){const n=V(),i=D(),r=R(()=>n.value?.find(a=>a.id===i.value));async function o(a){f.runTerminalAction(await p(),a,"remove")}return L(()=>{!i.value&&n.value?.length&&(i.value=n.value[0].id)}),(a,s)=>{const k=F,w=$,u=U;return x(n)?.length?(t(),d("div",W,[c("div",Y,[(t(!0),d(N,null,S(x(n),e=>(t(),d("button",{key:e.id,border:"r base",flex:"~ gap-2","items-center":"",px3:"",py2:"",class:h(e.id===r.value?.id?"bg-active":""),onClick:l=>i.value=e.id},[e.icon?(t(),m(k,{key:0,icon:e.icon},null,8,["icon"])):v("",!0),c("span",{class:h(e.id===r.value?.id?"":"op50")},_(e.name)+_(e.isTerminated?" (terminated)":""),3),e.isTerminated?(t(),m(w,{key:1,icon:"carbon-close","mx--2":"",border:!1,onClick:j(l=>o(e.id),["stop"])},null,8,["onClick"])):v("",!0)],10,G))),128))]),r.value?(t(),m(u,{id:r.value.id,key:r.value.id},null,8,["id"])):(t(),d("div",H,[s[0]||(s[0]=C(" Terminal ")),c("code",null,_(x(i)),1),s[1]||(s[1]=C(" not found "))]))])):(t(),d("div",J,s[2]||(s[2]=[c("em",{op50:""},"No terminal attached",-1)])))}}}),ae=y({__name:"terminals",setup(b){return(n,i)=>{const r=K,o=z;return t(),m(o,null,{default:q(()=>[A(r)]),_:1})}}});export{ae as default};
