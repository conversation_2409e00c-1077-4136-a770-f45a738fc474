import{b1 as $e,b2 as le,D as Ee,b3 as Ge,b4 as Ke,S as Qe,H as Xe,Q as Ye,J as Ze,b as De,ai as et,b5 as tt,I as ot,q as nt,e as Re,v as at,c as Ve,R as st,b6 as lt,M as rt,p as it,W as ut}from"./d8ykqm74.js";import{_ as dt}from"./nnavbar.vue-lvh4ek84.js";import{_ as je}from"./nbadge-ntuzwfj6.js";import{k as ye,b as L,Q as r,S as k,U as i,Y as M,O as $,u as b,a5 as I,W as P,a6 as K,X as ct,V as pt,F as N,ab as W,z as me,c as Ue,g as Ie,D as ft,n as vt,r as ve,l as V,w as mt,af as Oe,$ as C,a0 as v,a8 as Z,a1 as yt,aw as ht,K as gt}from"./vendor/json-editor-vue-mdfcgt41.js";import{_ as bt}from"./nselect.vue-bpqzr4w7.js";import{_ as Ne}from"./server-route-inputs.vue-mdebn387.js";import{_ as _t}from"./code-snippets.vue-bs8dkort.js";import{_ as kt}from"./ncode-block.vue-bfbbo3c9.js";import{d as wt}from"./vendor/unocss-mnpxkox6.js";import{C as xe,S as Le}from"./constants-b32h69zq.js";import{_ as xt}from"./nsection-block-oxvuqj5d.js";import{_ as Ct}from"./ndrawer.vue-of9w84z3.js";import"./vendor/shiki-fpe2sz4h.js";import"./ncheckbox.vue-h2ndpucy.js";import"./client-klf75x38.js";import"./nicon-title.vue-g9trlv29.js";function St(e,l){if(typeof e!="string")throw new TypeError("argument str must be a string");const n={},o=l||{},y=o.decode||Tt;let f=0;for(;f<e.length;){const d=e.indexOf("=",f);if(d===-1)break;let m=e.indexOf(";",f);if(m===-1)m=e.length;else if(m<d){f=e.lastIndexOf(";",d-1)+1;continue}const g=e.slice(f,d).trim();if(o?.filter&&!o?.filter(g)){f=m+1;continue}if(n[g]===void 0){let u=e.slice(d+1,m).trim();u.codePointAt(0)===34&&(u=u.slice(1,-1)),n[g]=$t(u,y)}f=m+1}return n}function Tt(e){return e.includes("%")?decodeURIComponent(e):e}function $t(e,l){try{return l(e)}catch{return e}}const ae=/^[\u0009\u0020-\u007E\u0080-\u00FF]+$/;function Ce(e,l,n){const o=n||{},y=o.encode||encodeURIComponent;if(typeof y!="function")throw new TypeError("option encode is invalid");if(!ae.test(e))throw new TypeError("argument name is invalid");const f=y(l);if(f&&!ae.test(f))throw new TypeError("argument val is invalid");let d=e+"="+f;if(o.maxAge!==void 0&&o.maxAge!==null){const m=o.maxAge-0;if(Number.isNaN(m)||!Number.isFinite(m))throw new TypeError("option maxAge is invalid");d+="; Max-Age="+Math.floor(m)}if(o.domain){if(!ae.test(o.domain))throw new TypeError("option domain is invalid");d+="; Domain="+o.domain}if(o.path){if(!ae.test(o.path))throw new TypeError("option path is invalid");d+="; Path="+o.path}if(o.expires){if(!Et(o.expires)||Number.isNaN(o.expires.valueOf()))throw new TypeError("option expires is invalid");d+="; Expires="+o.expires.toUTCString()}if(o.httpOnly&&(d+="; HttpOnly"),o.secure&&(d+="; Secure"),o.priority)switch(typeof o.priority=="string"?o.priority.toLowerCase():o.priority){case"low":{d+="; Priority=Low";break}case"medium":{d+="; Priority=Medium";break}case"high":{d+="; Priority=High";break}default:throw new TypeError("option priority is invalid")}if(o.sameSite)switch(typeof o.sameSite=="string"?o.sameSite.toLowerCase():o.sameSite){case!0:{d+="; SameSite=Strict";break}case"lax":{d+="; SameSite=Lax";break}case"strict":{d+="; SameSite=Strict";break}case"none":{d+="; SameSite=None";break}default:throw new TypeError("option sameSite is invalid")}return o.partitioned&&(d+="; Partitioned"),d}function Et(e){return Object.prototype.toString.call(e)==="[object Date]"||e instanceof Date}const Pe=ye({__name:"ServerRouteListItem",props:{item:{},index:{default:0}},setup(e){const l=L(!0),n=$e();return(o,y)=>{const f=Ee,d=je,m=Pe;return r(),k("div",null,[i("button",{flex:"~ gap-2","w-full":"","items-start":"","items-center":"","hover-bg-active":"",px2:"",py1:"",class:M([{"bg-active":b(n)===o.item.filepath}]),style:ct({paddingLeft:`calc(0.5rem + ${o.index*1.5}em)`}),onClick:y[0]||(y[0]=g=>{l.value=!l.value,n.value=o.item.filepath})},[i("div",{class:M({"w-12":!o.item.routes}),"flex-none":"","text-left":""},[o.item.type==="collection"?(r(),$(f,{key:0,icon:"carbon:chevron-right","mb0.5":"","transform-rotate":l.value?90:0,transition:""},null,8,["transform-rotate"])):(r(),$(d,{key:1,class:M(b(le)(o.item.method||"*")),textContent:I((o.item.method||"*").toUpperCase())},null,8,["class","textContent"]))],2),i("span",{class:M({"flex items-center":o.item.routes}),"text-sm":"","font-mono":""},[o.item.type==="collection"?(r(),$(f,{key:0,title:`${o.item.routes?.length} routes`,icon:"carbon:folder",mr1:""},null,8,["title"])):P("",!0),K(" "+I(o.item.route),1)],2)],6),y[1]||(y[1]=i("div",{"x-divider":""},null,-1)),l.value?pt(o.$slots,"default",{key:0},()=>[(r(!0),k(N,null,W(o.item.routes,g=>(r(),$(m,{key:g.filepath,item:g,index:o.index+1},null,8,["item","index"]))),128))]):P("",!0)])}}});function B(e){if(typeof e!="object")return e;var l,n,o=Object.prototype.toString.call(e);if(o==="[object Object]"){if(e.constructor!==Object&&typeof e.constructor=="function"){n=new e.constructor;for(l in e)e.hasOwnProperty(l)&&n[l]!==e[l]&&(n[l]=B(e[l]))}else{n={};for(l in e)l==="__proto__"?Object.defineProperty(n,l,{value:B(e[l]),configurable:!0,enumerable:!0,writable:!0}):n[l]=B(e[l])}return n}if(o==="[object Array]"){for(l=e.length,n=Array(l);l--;)n[l]=B(e[l]);return n}return o==="[object Set]"?(n=new Set,e.forEach(function(y){n.add(B(y))}),n):o==="[object Map]"?(n=new Map,e.forEach(function(y,f){n.set(B(f),B(y))}),n):o==="[object Date]"?new Date(+e):o==="[object RegExp]"?(n=new RegExp(e.source,e.flags),n.lastIndex=e.lastIndex,n):o==="[object DataView]"?new e.constructor(B(e.buffer)):o==="[object ArrayBuffer]"?e.slice(0):o.slice(-6)==="Array]"?new e.constructor(e):e}const Dt={path:"/",watch:!0,decode:e=>wt(decodeURIComponent(e)),encode:e=>encodeURIComponent(typeof e=="string"?e:JSON.stringify(e))},se=window.cookieStore;function Rt(e,l){const n={...Dt,...l};n.filter??=g=>g===e;const o=Se(n)||{};let y;n.maxAge!==void 0?y=n.maxAge*1e3:n.expires&&(y=n.expires.getTime()-Date.now());const f=y!==void 0&&y<=0,d=B(f?void 0:o[e]??n.default?.()),m=y&&!f?Ut(d,y,n.watch&&n.watch!=="shallow"):L(d);{let g=null;try{!se&&typeof BroadcastChannel<"u"&&(g=new BroadcastChannel(`nuxt:cookies:${e}`))}catch{}const u=()=>{n.readonly||Ge(m.value,o[e])||(jt(e,m.value,n),o[e]=B(m.value),g?.postMessage({value:n.encode(m.value)}))},E=A=>{const S=A.refresh?Se(n)?.[e]:n.decode(A.value);U=!0,m.value=S,o[e]=B(S),vt(()=>{U=!1})};let U=!1;const F=!!Ie();if(F&&me(()=>{U=!0,u(),g?.close()}),se){const A=S=>{const _=S.changed.find(h=>h.name===e),O=S.deleted.find(h=>h.name===e);_&&E({value:_.value}),O&&E({value:null})};se.addEventListener("change",A),F&&me(()=>se.removeEventListener("change",A))}else g&&(g.onmessage=({data:A})=>E(A));n.watch?Ue(m,()=>{U||u()},{deep:n.watch!=="shallow"}):u()}return m}function Se(e={}){return St(document.cookie,e)}function Vt(e,l,n={}){return l==null?Ce(e,l,{...n,maxAge:-1}):Ce(e,l,n)}function jt(e,l,n={}){document.cookie=Vt(e,l,n)}const Te=2147483647;function Ut(e,l,n){let o,y,f=0;const d=n?L(e):{value:e};return Ie()&&me(()=>{y?.(),clearTimeout(o)}),ft((m,g)=>{n&&(y=Ue(d,g));function u(){f=0,clearTimeout(o);const E=l-f,U=E<Te?E:Te;o=setTimeout(()=>{if(f+=U,f<l)return u();d.value=void 0,g()},U)}return{get(){return m(),d.value},set(E){u(),d.value=E,g()}}})}const It={"h-full":"","w-full":"",flex:"~ col"},Ot={flex:"~ col gap-2","flex-none":"",p4:"","n-navbar-glass":""},Nt={flex:"~ gap2 items-center"},Lt={relative:"","w-full":""},Pt={absolute:"","right-2":"","top-1.5":"",flex:"~ gap-1"},At={flex:"~ gap2 wrap","w-full":"","items-center":"",px4:"",pb2:"","text-center":"","text-sm":"",border:"b base"},Bt={class:"hidden md:block"},qt={key:0,border:"b base","items-center":"",px4:"",py2:"",grid:"~ cols-[max-content_1fr] gap-2"},Mt={"text-right":"","font-mono":""},Ft={key:1,border:"b base",p4:"",flex:"~ col gap-4","font-mono":""},Ht={flex:"~ gap-4"},zt={flex:"~ gap2","mb--2":"","items-center":"",op50:""},Wt={key:2},Jt={key:3,border:"b base",relative:"","n-code-block":""},Gt={flex:"~ wrap","w-full":""},Kt=["onClick"],Qt={border:"b base",flex:"~ gap2","items-center":"",px4:"",py2:""},Xt={key:1,"text-xs":"",op50:""},Yt={key:0,"flex-auto":"","overflow-auto":""},Zt={border:"~ base","h-full":"","w-full":"",rounded:""},eo=["data"],to={key:2,"flex-auto":"","overflow-auto":"",p4:""},oo={border:"~ base",rounded:""},no=["src"],ao={key:1,controls:"",rounded:""},so=["src"],lo=ye({__name:"ServerRouteDetails",props:{route:{}},emits:["open-default-input"],setup(e,{emit:l}){const n=e,o=l,[y,f]=Ke(),d=Qe(),m=Xe(),g=Ye(),u=ve({contentType:"text/plain",data:"",statusCode:200,error:void 0,fetchTime:0}),E=V(()=>u.contentType.includes("application/json")?"json":u.contentType.includes("text/html")?"html":u.contentType.includes("text/css")?"css":u.contentType.includes("text/javascript")?"javascript":u.contentType.includes("image")||u.contentType.includes("video")?"media":u.contentType.includes("text/xml")||u.contentType.includes("application/xml")?"xml":u.contentType.includes("application/pdf")?"pdf":"text"),U=V(()=>{if(E.value==="json")return JSON.stringify(u.data,null,2);if(E.value==="media"||E.value==="pdf"){const a=new Blob([u.data],{type:u.contentType});return URL.createObjectURL(a)}return u.data}),F=L(!1),A=L(!1),S=Ze(),_=V(()=>n.route.route?.split(/((?:\*\*)?:\w+)/g)),O=V(()=>_.value?.filter(a=>a.startsWith(":")||a.startsWith("**:"))||[]),h=L(n.route.method||"GET"),D=L({}),w=ve({query:[{active:!0,key:"",value:"",type:"string"}],body:[{active:!0,key:"",value:"",type:"string"}],headers:[{active:!0,key:"Content-Type",value:"application/json",type:"string"}]}),j=L({}),{inputDefaults:x,sendFrom:H}=De("serverRoutes"),Q=V(()=>g?.value?.app?.$fetch?H.value:"devtools"),re=["GET","POST","PUT","PATCH","DELETE","HEAD"],ie=["PATCH","POST","PUT","DELETE"],ee=V(()=>ie.includes(h.value.toUpperCase())),c=L(),te=["input","json"],J=L(te[0]),ue=V({get:()=>w[c.value],set:a=>{w[c.value]=a}}),he=V(()=>({...G(x.value.query),...G(w.query)})),ge=V(()=>({...G(x.value.headers),...G(w.headers)})),de=V(()=>ee.value?J.value==="json"?{...G(x.value.body),...j.value}:{...G(x.value.body),...G(w.body)}:void 0),Ae=V(()=>{let a=window?.location.origin;return a.charAt(a.length-1)==="/"&&(a=a.slice(0,-1)),a}),oe=V(()=>{let a=new URLSearchParams(he.value).toString();a&&(a=`?${a}`);const t=(_.value?.map(R=>(R.startsWith(":")||R.startsWith("**:"))&&D.value[R]||R).join("")||"")+a;let p=m.value?.app.baseURL||"";return(p==="./"||p===".")&&(p=""),p.endsWith("/")&&(p=p.slice(0,-1)),p+t}),be=V(()=>Ae.value+oe.value);function G(a){const t=Object.fromEntries(a.filter(({active:p,key:R,value:T})=>p&&R&&T!==void 0).map(({key:p,value:R})=>[p,R]));return Object.entries(t).length?t:void 0}async function _e(){A.value=!0,F.value=!0;const a=Date.now(),t=Q.value==="app"?g.value.app.$fetch:$fetch;nt("server-routes:fetch",{method:h.value,sendFrom:Q.value});try{u.data=await t(be.value,{method:h.value.toUpperCase(),headers:ge.value,query:he.value,body:de.value,onResponse({response:p}){u.contentType=(p.headers.get("content-type")||"").toString().toLowerCase().trim(),u.statusCode=p.status,u.error=void 0},onResponseError(p){u.error=p.response._data,u.data=p.response._data}})}catch{}F.value=!1,u.fetchTime=Date.now()-a}const ke=V(()=>{const a=[],t=[],p=Object.entries(ge.value).filter(([T,q])=>T&&q&&!(T==="Content-Type"&&q==="application/json")).map(([T,q])=>`  '${T}': '${q}'`).join(`,
`);h.value.toUpperCase()!=="GET"&&t.push(`method: '${h.value.toUpperCase()}'`),p&&t.push(`headers: {
${p}
}`),de.value&&t.push(`body: ${JSON.stringify(de.value,null,2)}`);const R=t.length?`, {
${t.join(`,
`).split(`
`).map(T=>`  ${T}`).join(`
`)}
}`:"";return a.push({name:"useFetch",lang:"javascript",docs:xe.nuxt.useFetch,code:`const { data, pending, error, refresh } = useFetch('${oe.value}'${R})`}),a.push({name:"$fetch",lang:"javascript",docs:xe.nuxt.$fetch,code:`await $fetch('${oe.value}'${R})`}),a}),X=L(qe()),z=ve({key:"",value:""}),Be=V(()=>{const a=[];return O.value.length&&a.push({name:"Params",slug:"params",length:O.value.length}),a.push({name:"Query",slug:"query",length:w.query.length}),ee.value&&a.push({name:"Body",slug:"body",length:w.body.length}),a.push({name:"Headers",slug:"headers",length:w.headers.length}),a.push({name:"Cookies",slug:"cookies",length:X.value.length}),a.push({name:"Snippets",slug:"snippet"}),a});function qe(){return document.cookie.split("; ").map(a=>{const[t,p]=a.split("=");return{key:t,value:p}}).filter(a=>a.key)}function ce(a,t){if(!a)return;const p=X.value.find(T=>T.key===a),R=Rt(a);p!==void 0?t===void 0&&(X.value=X.value.filter(T=>T.key!==a)):(X.value.push({key:a,value:t}),z.key="",z.value=""),R.value=t}mt(()=>{J.value==="json"&&typeof j.value=="string"&&(j.value=JSON.parse(j.value))});const pe=et("nuxt-devtools:server-routes:inputs",()=>[],{window:window.parent});tt([w,c],()=>{const a=pe.value?.find(t=>t.path===n.route.filepath);if(a){c.value||(c.value=a.tab),a.tab!==c.value&&(a.tab=c.value);const{body:t,query:p,headers:R,params:T}=a.inputs;Object.assign(w,{body:t,query:p,headers:R}),D.value=T}else{const t={path:n.route.filepath,tab:O.value.length?"params":"query",inputs:{...w,...O.value.length?{params:D.value}:{}}};pe.value.push(t),c.value||(c.value=t.tab)}},{immediate:!0,deep:!0,debounce:500});function Me(){pe.value=[],w.body=[],w.query=[],w.headers=[],D.value={},c.value=O.value.length?"params":"query"}const Fe=ot();return(a,t)=>{const p=Re,R=bt,T=at,q=Ee,we=Ne,He=_t,ze=Ve,We=st,fe=je,Je=kt,ne=Oe("tooltip");return r(),k("div",It,[i("div",Ot,[i("div",Nt,[a.route.method?(r(),$(p,{key:0,class:M(["n-badge-base n-sm",b(le)(h.value)]),"pointer-events-none":"","font-mono":"",tabindex:"-1"},{default:C(()=>[K(I(h.value.toUpperCase()),1)]),_:1},8,["class"])):(r(),$(R,{key:1,modelValue:h.value,"onUpdate:modelValue":t[0]||(t[0]=s=>h.value=s),class:M(["n-badge-base n-sm",b(le)(h.value)])},{default:C(()=>[(r(),k(N,null,W(re,s=>i("option",{key:s,class:M(b(le)(s))},I(s.toUpperCase()),3)),64))]),_:1},8,["modelValue","class"])),i("div",Lt,[v(T,{"model-value":oe.value,readonly:"","flex-auto":"","font-mono":"",p:"x5 y2",n:"sm"},null,8,["model-value"]),i("div",Pt,[Z(v(p,{title:"Copy URL",n:"xs blue",icon:"carbon:copy",border:!1,onClick:t[1]||(t[1]=s=>b(Fe)(be.value,"server-route-url"))},null,512),[[ne,"Copy URL"]]),Z(v(p,{title:"Open in Editor",icon:"carbon-launch",n:"xs blue",border:!1,onClick:t[2]||(t[2]=s=>b(S)(a.route.filepath))},null,512),[[ne,"Open in Editor"]])])]),v(p,{"h-full":"",n:"primary solid",onClick:_e},{default:C(()=>[v(q,{icon:"carbon:send"})]),_:1})])]),i("div",At,[(r(!0),k(N,null,W(Be.value,s=>Z((r(),$(p,{key:s.slug,class:M(c.value===s.slug?"text-primary n-primary":"border-transparent shadow-none"),onClick:Y=>c.value=s.slug},{default:C(()=>[v(q,{icon:b(Le)[s.slug]},null,8,["icon"]),i("div",Bt,[K(I(s.name)+" "+I(s?.length?`(${s.length})`:"")+" ",1),i("span",null,I(b(x)[s.slug]?.length?`(${b(x)[s.slug].length})`:""),1)])]),_:2},1032,["class","onClick"])),[[ne,s.name]])),128)),t[12]||(t[12]=i("div",{"flex-auto":""},null,-1)),t[13]||(t[13]=i("div",{"text-xs":"",op50:""}," Send from ",-1)),v(R,{modelValue:Q.value,"onUpdate:modelValue":t[3]||(t[3]=s=>Q.value=s),class:"n-xs",disabled:!b(g)?.app?.$fetch},{default:C(()=>t[11]||(t[11]=[i("option",{value:"app"}," App ",-1),i("option",{value:"devtools"}," DevTools ",-1)])),_:1},8,["modelValue","disabled"]),Z(v(p,{n:"orange",class:"p-3",icon:"i-carbon-clean",onClick:Me},null,512),[[ne,"Clear Inputs Saved Cache"]])]),c.value==="params"?(r(),k("div",qt,[(r(!0),k(N,null,W(O.value,s=>(r(),k(N,{key:s},[i("div",Mt,I(s),1),v(T,{modelValue:D.value[s],"onUpdate:modelValue":Y=>D.value[s]=Y,placeholder:s,"flex-1":""},null,8,["modelValue","onUpdate:modelValue","placeholder"])],64))),128))])):P("",!0),c.value==="cookies"?(r(),k("div",Ft,[(r(!0),k(N,null,W(X.value,s=>(r(),k("div",{key:s.key,flex:"~ gap-4 items-center"},[v(T,{placeholder:"Key...","model-value":s.key,disabled:"","op-70":""},null,8,["model-value"]),v(T,{placeholder:"Value...","model-value":s.value,"flex-1":"",n:"primary",onInput:Y=>ce(s.key,Y.target?.value)},null,8,["model-value","onInput"]),v(p,{title:"Delete",n:"red",onClick:Y=>ce(s.key,void 0)},{default:C(()=>[v(q,{icon:"i-carbon-trash-can"})]),_:2},1032,["onClick"])]))),128)),i("div",Ht,[v(T,{modelValue:z.key,"onUpdate:modelValue":t[4]||(t[4]=s=>z.key=s),placeholder:"Key",n:"primary","flex-1":""},null,8,["modelValue"]),v(T,{modelValue:z.value,"onUpdate:modelValue":t[5]||(t[5]=s=>z.value=s),placeholder:"Value",n:"primary","flex-1":""},null,8,["modelValue"]),v(p,{title:"Add",n:"primary",onClick:t[6]||(t[6]=s=>ce(z.key,z.value))},{default:C(()=>[v(q,{icon:"i-carbon-save"})]),_:1})])])):P("",!0),v(b(y),null,{default:C(()=>[v(we,{modelValue:ue.value,"onUpdate:modelValue":t[9]||(t[9]=s=>ue.value=s),default:{active:!0,type:"string"},"max-h-xs":"","of-auto":""},{default:C(()=>[b(x)[c.value]?.length?(r(),k(N,{key:0},[i("div",zt,[t[14]||(t[14]=i("div",{"w-5":"","x-divider":""},null,-1)),t[15]||(t[15]=i("div",{"flex-none":""}," Default Inputs ",-1)),v(p,{icon:"i-carbon-edit",border:!1,onClick:t[7]||(t[7]=s=>o("open-default-input"))}),t[16]||(t[16]=i("div",{"x-divider":""},null,-1))]),v(we,{modelValue:b(x)[c.value],"onUpdate:modelValue":t[8]||(t[8]=s=>b(x)[c.value]=s),disabled:"",p0:""},null,8,["modelValue"])],64)):P("",!0)]),_:1},8,["modelValue"])]),_:1}),c.value==="snippet"?(r(),k("div",Wt,[ke.value.length?(r(),$(He,{key:0,"code-snippets":ke.value},null,8,["code-snippets"])):P("",!0)])):ue.value?(r(),k("div",Jt,[c.value==="body"?(r(),k(N,{key:0},[i("div",Gt,[(r(),k(N,null,W(te,s=>i("button",{key:s,px4:"",py2:"",border:"r base",hover:"bg-active",class:M({"border-b":s!==J.value}),onClick:Y=>J.value=s},[i("div",{class:M({op30:s!==J.value}),"font-mono":""},I(s),3)],10,Kt)),64)),t[17]||(t[17]=i("div",{border:"b base","flex-auto":""},null,-1))]),J.value==="input"?(r(),$(b(f),{key:0})):J.value==="json"?(r(),$(b(ht),yt({key:1,modelValue:j.value,"onUpdate:modelValue":t[10]||(t[10]=s=>j.value=s),class:[b(d)==="dark"?"jse-theme-dark":"light","json-editor-vue of-auto text-sm outline-none"]},a.$attrs,{mode:"text","navigation-bar":!1,indentation:2,"tab-size":2}),null,16,["modelValue","class"])):P("",!0)],64)):(r(),$(b(f),{key:1}))])):P("",!0),A.value?F.value?(r(),$(We,{key:5,"z-10":"","flex-auto":"","backdrop-blur":""},{default:C(()=>t[19]||(t[19]=[K(" Fetching... ")])),_:1})):(r(),k(N,{key:6},[i("div",Qt,[t[21]||(t[21]=i("div",null,"Response",-1)),u.error?(r(),$(fe,{key:0,n:"red"},{default:C(()=>t[20]||(t[20]=[K(" Error ")])),_:1})):P("",!0),v(fe,{n:u.error?"orange":"green",textContent:I(u.statusCode)},null,8,["n","textContent"]),u.contentType?(r(),k("code",Xt,I(u.contentType),1)):P("",!0),t[22]||(t[22]=i("div",{"flex-auto":""},null,-1)),t[23]||(t[23]=i("div",{op50:""}," Request finished in ",-1)),v(fe,{n:"green"},{default:C(()=>[K(I(u.fetchTime)+" ms ",1)]),_:1})]),E.value==="pdf"?(r(),k("div",Yt,[i("div",Zt,[i("object",{data:U.value,type:"application/pdf","flex-auto":"",width:"100%",height:"100%",rounded:""},null,8,eo)])])):E.value!=="media"?(r(),$(Je,{key:1,"flex-auto":"","overflow-auto":"","py-2":"",code:U.value,lang:E.value},null,8,["code","lang"])):(r(),k("div",to,[i("div",oo,[u.contentType.includes("image")?(r(),k("img",{key:0,rounded:"",src:U.value},null,8,no)):(r(),k("video",ao,[i("source",{src:U.value,type:"video/mp4"},null,8,so)]))])]))],64)):(r(),$(ze,{key:4},{default:C(()=>[v(p,{n:"primary",onClick:_e},{default:C(()=>[v(q,{icon:"carbon:send"}),t[18]||(t[18]=K(" Send request "))]),_:1})]),_:1}))])}}}),ro={flex:"~ gap1","text-sm":""},io={key:0,op50:""},uo={op50:""},$o=ye({__name:"server-routes",setup(e){const l=L(!1),n=lt(),o=$e(),{selectedRoute:y,view:f,inputDefaults:d}=De("serverRoutes"),m=V(()=>{!o.value&&y.value&&(o.value=y.value.filepath);const S=n.value?.find(_=>_.filepath===o.value);return o.value!==y.value?.filepath&&S&&(y.value=S),S}),g=L(""),u=V(()=>new rt(n.value||[],{keys:["method","route"],shouldSort:!0})),E=V(()=>n.value?g.value?u.value.search(g.value).map(_=>_.item):n.value:[]),U=V(()=>{const S=[],_=(h,D)=>{h.routes=h.routes||[],h.routes.push(D)},O=(h,D)=>{const w=D?D.routes?.find(x=>x.route===h):S.find(x=>x.route===h);if(w)return w;const j={route:h,filepath:h.replace(/\W/g,"-").toLowerCase(),type:"collection",routes:[]};return D?_(D,j):S.push(j),j};return E.value.forEach(h=>{let D,w;const j=h.filepath.split("/"),x=j.slice(j.indexOf("server")+1);if(h.type==="runtime"){x[0]="runtime";const H=j.indexOf("dist");H!==-1&&(D=j[H-1],D&&x.splice(1,0,D))}x.length>0&&x[x.length-1].includes(".")&&x.pop(),x.forEach(H=>{w=O(H,w)}),w?_(w,h):S.push(h)}),S});function F(){f.value=f.value==="tree"?"list":"tree"}function A(S){return S.charAt(0).toUpperCase()+S.slice(1)}return(S,_)=>{const O=Re,h=dt,D=Pe,w=lo,j=ut,x=Ve,H=it,Q=Ne,re=xt,ie=Ct,ee=Oe("tooltip");return r(),k(N,null,[v(H,{"storage-key":"tab-server-routes"},{left:C(()=>[v(h,{search:g.value,"onUpdate:search":_[1]||(_[1]=c=>g.value=c),pb2:""},{actions:C(()=>[Z(v(O,{"text-lg":"",icon:b(f)==="list"?"i-carbon-list":"i-carbon-tree-view-alt",title:"Toggle view",border:!1,onClick:F},null,8,["icon"]),[[ee,"Toggle View"]]),Z(v(O,{"text-lg":"",icon:"i-carbon-cics-sit-overrides",title:"Default Inputs",border:!1,onClick:_[0]||(_[0]=c=>l.value=!l.value)},null,512),[[ee,"Default Inputs"]])]),default:C(()=>[i("div",ro,[g.value?(r(),k("span",io,I(E.value.length)+" matched · ",1)):P("",!0),i("span",uo,I(b(n)?.length)+" routes in total",1)])]),_:1},8,["search"]),(r(!0),k(N,null,W(b(f)==="tree"?U.value:E.value,c=>(r(),$(D,{key:c.filepath,item:c},null,8,["item"]))),128))]),right:C(()=>[(r(),$(gt,{max:10},[m.value?(r(),$(w,{key:m.value.filepath,route:m.value,onOpenDefaultInput:_[2]||(_[2]=c=>l.value=!0)},null,8,["route"])):P("",!0)],1024)),m.value?P("",!0):(r(),$(x,{key:0},{default:C(()=>[v(j,{px6:"",py2:""},{default:C(()=>_[5]||(_[5]=[i("span",{op75:""},"Select a route to start",-1)])),_:1})]),_:1}))]),_:1}),v(ie,{modelValue:l.value,"onUpdate:modelValue":_[3]||(_[3]=c=>l.value=c),"auto-close":"","max-w-xl":"","min-w-xl":"",onClose:_[4]||(_[4]=c=>l.value=!1)},{default:C(()=>[i("div",null,[_[6]||(_[6]=i("div",{p4:"",border:"b base"},[i("span",{"text-lg":""},"Default Inputs"),i("br"),i("span",{"text-white":"",op50:""},"Merged as default for every request in DevTools")],-1)),(r(!0),k(N,null,W(Object.keys(b(d)),c=>(r(),$(re,{key:c,text:`${A(c)} ${b(d)[c].length?`(${b(d)[c].length})`:""}`,padding:!1,icon:b(Le)[c]},{default:C(()=>[v(Q,{modelValue:b(d)[c],"onUpdate:modelValue":te=>b(d)[c]=te,py0:"",default:{active:!0,type:"string"}},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["text","icon"]))),128))])]),_:1},8,["modelValue"])],64)}}});export{$o as default};
