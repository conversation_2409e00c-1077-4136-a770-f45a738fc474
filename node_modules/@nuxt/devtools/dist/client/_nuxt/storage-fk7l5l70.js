import{S as ne,b9 as ae,l as oe,m as T,z as se,r as f,h as k,ba as z,e as le,v as re,D as ue,W as ie,c as de,p as me,a5 as pe}from"./d8ykqm74.js";import{_ as ve}from"./nselect.vue-bpqzr4w7.js";import{_ as fe}from"./nlink.vue-jt5fxemk.js";import{_ as ce}from"./filepath-item.vue-n7veglgp.js";import{k as W,b as U,ax as E,y as ye,c as ge,w as _e,l as ke,u as c,Q as s,O as p,$ as r,U as l,a0 as d,A as be,S as u,F as x,ab as A,a5 as b,Y as F,Z as M,a6 as i,W as R,aw as xe,a1 as we,a8 as Ce,ah as Se,am as Ke}from"./vendor/json-editor-vue-mdfcgt41.js";import"./vendor/unocss-mnpxkox6.js";import"./vendor/shiki-fpe2sz4h.js";const Ve={class:"h-[48px] flex items-center justify-between gap1 px-3"},$e={class:"w-full text-sm"},Ne=["value"],Ie=["onClick"],he={key:0,"h-full":"","of-hidden":"",flex:"~ col"},De={border:"b base",class:"h-[49px] flex flex-none items-center justify-between px-4 text-sm"},je={class:"flex items-center gap-4"},Ue={key:1},Ae=["onKeyup"],Me={key:0,op50:""},Be={key:1},Le={"font-bold":""},qe={"text-sm":""},Pe=W({__name:"StorageDetails",async setup(O){let m,g;const w=ne(),C=ae(),S=oe(),K=U(""),_=U(""),o=T("storage:current",""),t=U(),y=T("storage:file:state",""),{data:V}=([m,g]=E(()=>z("storageMounts",()=>f.getStorageMounts())),m=await m,g(),m),{data:$,refresh:B}=([m,g]=E(async()=>z("storageKeys",async()=>o.value?await f.getStorageKeys(o.value):[])),m=await m,g(),m),G=C.hook("storage:key:update",async(n,e)=>{if(!(!o.value||n.split(":")[0]!==o.value)&&(await B(),y.value===n)){if(e==="remove")return S.replace({query:{storage:o.value}});await I(y.value)}});ye(G),ge(o,()=>{B(),y.value=""}),_e(async()=>{if(!y.value){t.value=null;return}I(y.value)}),se("keydown",n=>{n.key==="s"&&(n.ctrlKey||n.metaKey)&&(h(),n.preventDefault())});function N(n){return n.replace(`${o.value}:`,"")}const Q=ke(()=>$.value?$.value.filter(n=>n.includes(K.value)):[]);async function I(n){const e=await f.getStorageItem(await k(),n);t.value={key:n,updatedKey:N(n),editingKey:!1,content:e,updatedContent:e}}async function Y(){if(!_.value||!o.value)return;const n=`${o.value}:${_.value}`;$.value?.includes(n)||await f.setStorageItem(await k(),n,""),S.replace({query:{storage:o.value,key:n}}),_.value=""}async function h(){t.value&&(await f.setStorageItem(await k(),t.value.key,t.value.updatedContent),await I(t.value.key))}async function Z(){!t.value||!o.value||(await f.removeStorageItem(await k(),t.value.key),t.value=null)}async function H(){if(!t.value||!o.value)return;const n=`${o.value}:${t.value.updatedKey}`,e=await k();await f.setStorageItem(e,n,t.value.updatedContent),await f.removeStorageItem(e,t.value.key),S.replace({query:{storage:o.value,key:n}})}return(n,e)=>{const D=le,J=ve,j=re,X=ue,L=fe,q=ie,P=de,ee=me,te=ce;return c(o)?(s(),p(ee,{key:0,"storage-key":"tab-storage"},{left:r(()=>[l("div",Ve,[d(D,{icon:"carbon-chevron-left","ml--1":"",border:!1,onClick:e[0]||(e[0]=a=>o.value="")}),l("div",$e,[d(J,{modelValue:c(o),"onUpdate:modelValue":e[1]||(e[1]=a=>be(o)?o.value=a:null),n:"primary",icon:"carbon-data-base"},{default:r(()=>[(s(!0),u(x,null,A(c(V),(a,v)=>(s(),u("option",{key:v,value:v},b(v),9,Ne))),128))]),_:1},8,["modelValue"])])]),d(j,{modelValue:K.value,"onUpdate:modelValue":e[2]||(e[2]=a=>K.value=a),icon:"carbon-search",placeholder:"Search...",n:"primary sm",border:"y x-none base! rounded-0",class:"w-full py2 ring-0!"},null,8,["modelValue"]),(s(!0),u(x,null,A(Q.value,a=>(s(),u(x,{key:a},[l("button",{block:"","w-full":"",truncate:"",px2:"",py1:"","text-start":"","text-sm":"","font-mono":"",class:F(a===t.value?.key?"text-primary n-bg-active":"text-secondary hover:n-bg-hover"),onClick:v=>y.value=a},b(N(a)),11,Ie),e[8]||(e[8]=l("div",{"x-divider":""},null,-1))],64))),128)),d(j,{modelValue:_.value,"onUpdate:modelValue":e[3]||(e[3]=a=>_.value=a),icon:"carbon-add",placeholder:"key",n:"sm",border:"t-none x-none base! rounded-0",class:"w-full py2 font-mono ring-0!",onKeyup:M(Y,["enter"])},null,8,["modelValue"])]),right:r(()=>[t.value?.key?(s(),u("div",he,[l("div",De,[l("div",je,[t.value.editingKey?(s(),p(j,{key:0,modelValue:t.value.updatedKey,"onUpdate:modelValue":e[4]||(e[4]=a=>t.value.updatedKey=a),onKeyup:M(H,["enter"])},null,8,["modelValue"])):(s(),u("code",Ue,[i(b(N(t.value.key))+" ",1),d(X,{icon:"carbon-edit",class:"cursor-pointer op50 hover:op100",onClick:e[5]||(e[5]=a=>t.value.editingKey=!0)})])),t.value.editingKey?R("",!0):(s(),p(D,{key:2,n:"green xs",disabled:t.value.content===t.value.updatedContent,class:F({"border-green":t.value.content!==t.value.updatedContent}),onClick:h},{default:r(()=>e[9]||(e[9]=[i(" Save ")])),_:1},8,["disabled","class"]))]),l("div",null,[d(D,{n:"red xs",onClick:Z},{default:r(()=>e[10]||(e[10]=[i(" Delete ")])),_:1})])]),typeof t.value.content=="object"?(s(),p(c(xe),we({key:0,modelValue:t.value.updatedContent,"onUpdate:modelValue":e[6]||(e[6]=a=>t.value.updatedContent=a),class:[[c(w)==="dark"?"jse-theme-dark":"light"],"json-editor-vue h-full of-auto text-sm outline-none"]},n.$attrs,{mode:"text","navigation-bar":!1,indentation:2,"tab-size":2}),null,16,["modelValue","class"])):Ce((s(),u("textarea",{key:1,"onUpdate:modelValue":e[7]||(e[7]=a=>t.value.updatedContent=a),placeholder:"Item value...",class:"h-full of-auto p-4 text-sm font-mono outline-none",onKeyup:M(Se(h,["ctrl"]),["enter"])},null,40,Ae)),[[Ke,t.value.updatedContent]])])):(s(),p(P,{key:1},{default:r(()=>[d(q,{px6:"",py4:""},{default:r(()=>[e[12]||(e[12]=i(" Select one key to start.")),e[13]||(e[13]=l("br",null,null,-1)),e[14]||(e[14]=i("Learn more about ")),d(L,{href:"https://nitro.unjs.io/guide/storage",n:"orange",target:"_blank"},{default:r(()=>e[11]||(e[11]=[i(" Nitro storage ")])),_:1})]),_:1})]),_:1}))]),_:1})):(s(),p(P,{key:1},{default:r(()=>[Object.keys(c(V)).length?(s(),u("p",Me," Select one storage to start: ")):(s(),u("p",Be,[e[16]||(e[16]=i(" No custom storage defined in ")),e[17]||(e[17]=l("code",null,"nitro.storage",-1)),e[18]||(e[18]=i(".")),e[19]||(e[19]=l("br",null,null,-1)),e[20]||(e[20]=i(" Learn more about ")),d(L,{href:"https://nitro.unjs.io/guide/storage",n:"orange",target:"_blank"},{default:r(()=>e[15]||(e[15]=[i(" Nitro storage ")])),_:1})])),(s(!0),u(x,null,A(c(V),(a,v)=>(s(),p(q,{key:v,"min-w-80":"","cursor-pointer":"","p-4":"","text-left":"",hover:"border-green",onClick:Te=>o.value=v},{default:r(()=>[l("span",Le,b(v),1),e[21]||(e[21]=l("br",null,null,-1)),l("span",qe,b(a.driver)+" driver",1),e[22]||(e[22]=l("br",null,null,-1)),a.base?(s(),p(te,{key:0,"text-xs":"",filepath:a.base},null,8,["filepath"])):R("",!0)]),_:2},1032,["onClick"]))),128))]),_:1}))}}}),Qe=W({__name:"storage",setup(O){return(m,g)=>{const w=Pe,C=pe;return s(),p(C,null,{default:r(()=>[d(w)]),_:1})}}});export{Qe as default};
