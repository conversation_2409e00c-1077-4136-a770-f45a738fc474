import{_ as j}from"./nbadge-ntuzwfj6.js";import{_ as F}from"./ncode-block.vue-bfbbo3c9.js";import{_ as L}from"./ncheckbox.vue-h2ndpucy.js";import{l as W,au as q,av as A,j as E,n as G,q as H,w as O,e as Q,x as Y,aw as Z,H as z,Y as J,Z as K,ax as X,at as ee}from"./d8ykqm74.js";import{k as te,b as P,Q as p,S as v,V as ae,u as o,a5 as $,W as w,F as B,a0 as s,O as ne,$ as u,U as _,a6 as c,l as oe}from"./vendor/json-editor-vue-mdfcgt41.js";const se={key:0},le={p4:"",flex:"~ col gap-1"},re={class:"text-lg font-medium leading-6"},ue={flex:"~ gap-3",mt2:"","justify-end":""},ve=te({__name:"NpmVersionCheck",props:{packageName:{},options:{},showVersion:{type:Boolean,default:!0}},setup(l){const a=l,t=W(),{info:n,update:i,state:g,processId:y,restart:m}=q(a.packageName,a.options),k=P(!0),N=P(!0),U=A(),x=E(),D=G();async function C(){const r=await i(async e=>x.start(e));H("npm:update",{packageName:a.packageName,oldVersion:n.value?.current}),r&&N.value&&U.value.push({id:r,message:`${a.packageName} has been updated. Do you want to restart the Nuxt server now?`}),r&&k.value&&(D.value=r,t.push("/modules/terminals"))}return(r,e)=>{const b=j,S=F,h=L,T=O,V=Q,I=Y;return p(),v(B,null,[ae(r.$slots,"default",{id:o(y),info:o(n),update:C,state:o(g),restart:o(m)},()=>[o(n)&&r.showVersion?(p(),v("code",se,$(`v${o(n).current}`),1)):w("",!0),o(n)?.latest?(p(),v(B,{key:1},[o(n).needsUpdate?(p(),v("button",{key:0,onClick:e[0]||(e[0]=f=>C())},[s(b,{n:"green",title:"updates available",textContent:"updates available"})])):(p(),ne(b,{key:1,n:"gray",title:"latest",textContent:"latest"}))],64)):w("",!0)]),s(o(x),null,{default:u(({resolve:f,args:R})=>[s(I,{"model-value":!0,onClose:d=>f(!1)},{default:u(()=>[_("div",le,[_("h3",re," Update "+$(a.packageName)+"? ",1),e[8]||(e[8]=_("p",{op50:""}," The following command will be executed in your terminal: ",-1)),s(S,{code:R[0],lang:"bash",my3:"",px4:"",py2:"",border:"~ base rounded",lines:!1},null,8,["code"]),s(h,{modelValue:k.value,"onUpdate:modelValue":e[1]||(e[1]=d=>k.value=d),n:"primary"},{default:u(()=>e[3]||(e[3]=[c(" Navigate to terminal ")])),_:1},8,["modelValue"]),s(h,{modelValue:N.value,"onUpdate:modelValue":e[2]||(e[2]=d=>N.value=d),n:"primary"},{default:u(()=>e[4]||(e[4]=[c(" Restart Nuxt server after update ")])),_:1},8,["modelValue"]),_("div",ue,[s(T,{n:"sm amber","flex-auto":"",icon:"i-carbon-data-backup"},{default:u(()=>e[5]||(e[5]=[c(" Please make sure to backup your project first. ")])),_:1}),s(V,{onClick:d=>f(!1)},{default:u(()=>e[6]||(e[6]=[c(" Cancel ")])),_:2},1032,["onClick"]),s(V,{n:"solid primary",onClick:d=>f(!0)},{default:u(()=>e[7]||(e[7]=[c(" Update ")])),_:2},1032,["onClick"])])])]),_:2},1032,["onClose"])]),_:1})],64)}}}),M=["pages","meta","components","imports","nuxt-config-schema","@nuxt/devtools","@nuxt/telemetry"];function ie(){return ee("getModulesList",async()=>(await $fetch("https://api.nuxt.com/modules?version=3")).modules.filter(a=>!M.includes(a.npm)&&a.compatibility.nuxt.includes(">=3")))}function _e(){return Z("installed-modules",()=>{const l=z(),a=ie();return oe(()=>(l.value?._installedModules||[]).map(t=>{if(!t.entryPath)return;const n=!!(t.entryPath&&J(t.entryPath)),i=t.meta?.name?t.meta?.name:t.entryPath?n?K(t.entryPath):l.value?.rootDir?X(t.entryPath,l.value?.rootDir).path:void 0:void 0,g=!!l.value?.modules?.includes(i),y=a.value?.find(m=>m.npm===i)||a.value?.find(m=>m.name===i);return{name:i,isPackageModule:n,isUninstallable:g,info:y,...t}}).filter(t=>t&&(!t.name||!M.includes(t.name))))})}export{ve as _,ie as a,_e as u};
