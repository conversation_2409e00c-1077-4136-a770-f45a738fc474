import{_ as J}from"./nicon-title.vue-g9trlv29.js";import{k as F,Q as r,S as b,a8 as X,ay as ee,u as o,U as l,A as f,Z as le,V as ne,w as L,a0 as t,F as k,ab as h,$ as s,O as M,a5 as _,Y as j,W as P,a6 as B}from"./vendor/json-editor-vue-mdfcgt41.js";import{y as te,b as oe,Q as ae,bj as se,a2 as ie,r as ue,e as de,W as re,t as I,bf as pe,bk as me}from"./d8ykqm74.js";import{_ as fe}from"./nselect.vue-bpqzr4w7.js";import{_ as ve}from"./ncheckbox.vue-h2ndpucy.js";import{_ as be}from"./nlink.vue-jt5fxemk.js";import"./vendor/unocss-mnpxkox6.js";import"./vendor/shiki-fpe2sz4h.js";const ce=["checked","disabled"],ge=["disabled"],xe=F({__name:"NSwitch",props:{modelValue:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1}},setup(z,{emit:g}){const d=te(z,"modelValue",g,{passive:!0});return(p,a)=>(r(),b("label",{class:"n-switch n-switch-base hover:n-switch-hover n-disabled:n-disabled",checked:o(d)||null,disabled:p.disabled||null},[X(l("input",{"onUpdate:modelValue":a[0]||(a[0]=v=>f(d)?d.value=v:null),type:"checkbox",class:"peer absolute op0",disabled:p.disabled,onKeypress:a[1]||(a[1]=le(v=>d.value=!o(d),["enter"]))},null,40,ge),[[ee,o(d)]]),a[2]||(a[2]=l("div",{class:"n-switch-slider n-checked:n-switch-slider-checked peer-active:n-active-base peer-focus-visible:n-focus-base n-transition"},[l("div",{class:"n-checked:n-switch-thumb-checked n-switch-thumb n-transition"})],-1)),ne(p.$slots,"default")],8,ce))}}),ye={px8:"",py6:""},ke={grid:"~ md:cols-2 gap-x-10 gap-y-3","max-w-300":""},Ve={flex:"~ col gap-2"},we={flex:"~ gap-2","flex-auto":"","items-center":"","justify-start":""},_e={capitalize:"",op75:""},Ce={flex:"~ col gap-2"},Se=["value"],Te=["value"],$e={flex:"~ gap-2"},Ne={flex:"~ gap-2"},Ee=F({__name:"settings",setup(z){const{interactionCloseOnOutsideClick:g,showPanel:C,showHelpButtons:S,scale:d,hiddenTabs:p,pinnedTabs:a,hiddenTabCategories:v,minimizePanelInactive:T,sidebarExpanded:$,sidebarScrollable:D}=oe("ui"),V=ae(),R=[["Tiny",12/15],["Small",14/15],["Normal",1],["Large",16/15],["Huge",18/15]],W=[["Always",0],["1s",1e3],["2s",2e3],["5s",5e3],["10s",1e4],["Never",-1]],H=se(ie());function K(i,e){e?p.value=p.value.filter(m=>m!==i):p.value.push(i)}function Q(i,e){e?v.value=v.value.filter(m=>m!==i):v.value.push(i)}function Y(i){a.value.includes(i)?a.value=a.value.filter(e=>e!==i):a.value.push(i)}function A(i,e){const m=a.value.indexOf(i);if(m===-1)return;const x=m+e;if(x<0||x>=a.value.length)return;const w=[...a.value];w.splice(m,1),w.splice(x,0,i),a.value=w}async function Z(){confirm("Are you sure you to reset all local settings & state? The app will reload.")&&(Object.keys(localStorage).forEach(i=>{i.startsWith("nuxt-devtools-")&&localStorage.removeItem(i)}),await ue.clearOptions(),V.value?.app?.reload?.(),window.location.reload())}return L(()=>{V.value&&(V.value.app.frameState.value.closeOnOutsideClick=g.value)}),L(()=>{V.value&&(V.value.app.frameState.value.minimizePanelInactive=T.value)}),(i,e)=>{const m=J,x=xe,w=pe,c=de,N=re,q=me,E=fe,y=ve,G=be;return r(),b("div",ye,[t(m,{class:"mb-5 text-xl op75",icon:"i-carbon-settings-adjust",text:"DevTools Settings"}),l("div",ke,[l("div",Ve,[e[10]||(e[10]=l("h3",{"text-lg":""}," Tabs ",-1)),(r(!0),b(k,null,h(o(H),([n,U])=>(r(),b(k,{key:n},[U.length?(r(),M(N,{key:0,p3:"",flex:"~ col gap-1",class:j(o(v).includes(n)?"op50 grayscale":"")},{default:s(()=>[t(x,{flex:"~ row-reverse",py1:"",pl2:"",pr1:"","n-lime":"","model-value":!o(v).includes(n),"onUpdate:modelValue":u=>Q(n,u)},{default:s(()=>[l("div",we,[l("span",_e,_(n),1)])]),_:2},1032,["model-value","onUpdate:modelValue"]),e[9]||(e[9]=l("div",{"mx--1":"",my1:"","h-1px":"",border:"b base",op75:""},null,-1)),(r(!0),b(k,null,h(U,u=>(r(),M(x,{key:u.name,flex:"~ row-reverse",py1:"",pl2:"",pr1:"","n-primary":"","model-value":!o(p).includes(u.name),"onUpdate:modelValue":O=>K(u.name,O)},{default:s(()=>[l("div",{flex:"~ gap-2","flex-auto":"","items-center":"","justify-start":"","pr-4":"",class:j(o(p).includes(u.name)?"op25":"")},[t(w,{"text-xl":"",icon:u.icon,title:u.title},null,8,["icon","title"]),l("span",null,_(u.title),1),e[8]||(e[8]=l("div",{"flex-auto":""},null,-1)),o(a).includes(u.name)?(r(),b(k,{key:0},[t(c,{icon:"i-carbon-caret-up",disabled:o(a).indexOf(u.name)===0,border:!1,onClick:O=>A(u.name,-1)},null,8,["disabled","onClick"]),t(c,{icon:"i-carbon-caret-down",disabled:o(a).indexOf(u.name)===o(a).length-1,border:!1,onClick:O=>A(u.name,1)},null,8,["disabled","onClick"])],64)):P("",!0),t(c,{icon:o(a).includes(u.name)?" i-carbon-pin-filled rotate--45":" i-carbon-pin op50",border:!1,onClick:O=>Y(u.name)},null,8,["icon","onClick"])],2)]),_:2},1032,["model-value","onUpdate:modelValue"]))),128))]),_:2},1032,["class"])):P("",!0)],64))),128))]),l("div",Ce,[e[27]||(e[27]=l("h3",{"text-lg":""}," Appearance ",-1)),t(N,{p4:"",flex:"~ col gap-2"},{default:s(()=>[l("div",null,[t(q,null,{default:s(({toggle:n,isDark:U})=>[t(c,{n:"primary",onClick:n},{default:s(()=>[e[11]||(e[11]=l("div",{"i-carbon-sun":"","dark:i-carbon-moon":"","translate-y--1px":""},null,-1)),B(" "+_(U.value?"Dark":"Light"),1)]),_:2},1032,["onClick"])]),_:1})]),e[14]||(e[14]=l("div",{"mx--2":"",my1:"","h-1px":"",border:"b base",op75:""},null,-1)),e[15]||(e[15]=l("p",null,"UI Scale",-1)),t(E,{modelValue:o(d),"onUpdate:modelValue":e[0]||(e[0]=n=>f(d)?d.value=n:null),n:"primary"},{default:s(()=>[(r(),b(k,null,h(R,n=>l("option",{key:n[0],value:n[1]},_(n[0]),9,Se)),64))]),_:1},8,["modelValue"]),e[16]||(e[16]=l("div",{"mx--2":"",my1:"","h-1px":"",border:"b base",op75:""},null,-1)),t(y,{modelValue:o($),"onUpdate:modelValue":e[1]||(e[1]=n=>f($)?$.value=n:null),"n-primary":""},{default:s(()=>e[12]||(e[12]=[l("span",null," Expand Sidebar ",-1)])),_:1},8,["modelValue"]),t(y,{modelValue:o(D),"onUpdate:modelValue":e[2]||(e[2]=n=>f(D)?D.value=n:null),disabled:o($),"n-primary":""},{default:s(()=>e[13]||(e[13]=[l("span",null," Scrollable Sidebar ",-1)])),_:1},8,["modelValue","disabled"])]),_:1}),e[28]||(e[28]=l("h3",{mt2:"","text-lg":""}," Features ",-1)),t(N,{p4:"",flex:"~ col gap-2"},{default:s(()=>[t(y,{modelValue:o(g),"onUpdate:modelValue":e[3]||(e[3]=n=>f(g)?g.value=n:null),"n-primary":""},{default:s(()=>e[17]||(e[17]=[l("span",null,"Close DevTools when clicking outside",-1)])),_:1},8,["modelValue"]),t(y,{modelValue:o(S),"onUpdate:modelValue":e[4]||(e[4]=n=>f(S)?S.value=n:null),"n-primary":""},{default:s(()=>e[18]||(e[18]=[l("span",null,"Show help buttons",-1)])),_:1},8,["modelValue"]),t(y,{modelValue:o(C),"onUpdate:modelValue":e[5]||(e[5]=n=>f(C)?C.value=n:null),"n-primary":""},{default:s(()=>e[19]||(e[19]=[l("span",null,"Always show the floating panel",-1)])),_:1},8,["modelValue"]),e[20]||(e[20]=l("div",{"mx--2":"",my1:"","h-1px":"",border:"b base",op75:""},null,-1)),e[21]||(e[21]=l("p",null,"Minimize floating panel on inactive",-1)),t(E,{modelValue:o(T),"onUpdate:modelValue":e[6]||(e[6]=n=>f(T)?T.value=n:null),modelModifiers:{number:!0},"n-primary":""},{default:s(()=>[(r(),b(k,null,h(W,n=>l("option",{key:n[0],value:n[1]},_(n[0]),9,Te)),64))]),_:1},8,["modelValue"])]),_:1}),e[29]||(e[29]=l("h3",{mt2:"","text-lg":""}," Feedback ",-1)),t(N,{p4:"",flex:"~ col gap-2"},{default:s(()=>[t(y,{modelValue:o(I),"onUpdate:modelValue":e[7]||(e[7]=n=>f(I)?I.value=n:null),"n-primary":""},{default:s(()=>[e[22]||(e[22]=l("span",null,"Send anonymous statistics, help us improving DevTools",-1)),t(G,{href:"https://github.com/nuxt/devtools#anonymous-usage-analytics",target:"_blank",ml1:"",op50:"",textContent:"Learn more"})]),_:1},8,["modelValue"]),e[25]||(e[25]=l("div",{"mx--2":"",my1:"","h-1px":"",border:"b base",op75:""},null,-1)),l("div",$e,[t(c,{n:"blue",to:"https://github.com/nuxt/devtools/discussions/29",target:"_blank"},{default:s(()=>e[23]||(e[23]=[l("div",{"i-carbon-data-enrichment":""},null,-1),B(" Ideas & Suggestions ")])),_:1}),t(c,{n:"orange",to:"https://github.com/nuxt/devtools/issues",target:"_blank"},{default:s(()=>e[24]||(e[24]=[l("div",{"i-carbon-debug":""},null,-1),B(" Bug Reports ")])),_:1})])]),_:1}),e[30]||(e[30]=l("h3",{mt2:"","text-lg":""}," Debug ",-1)),l("div",Ne,[t(c,{n:"orange",onClick:Z},{default:s(()=>e[26]||(e[26]=[l("div",{"i-carbon-breaking-change":""},null,-1),B(" Reset Local Settings & State ")])),_:1})])])])])}}});export{Ee as default};
