import{y as S,S as C,e as _}from"./d8ykqm74.js";import{k as N,j as b,B as O,c as j,af as M,Q as i,S as p,U as d,Y as u,u as a,a5 as k,W as m,V as R,a2 as $,a3 as z,a8 as D,O as g,F as E,a1 as P,aw as A,n as F}from"./vendor/json-editor-vue-mdfcgt41.js";const J=["open"],U={flex:"~ gap2","select-none":"","items-center":"",px4:""},x={key:0,class:"bg-red:10 px5 py3 text-red"},Y=N({__name:"StateEditor",props:{name:{},open:{type:Boolean},revision:{},state:{},readonly:{type:Boolean}},emits:["update:open"],setup(h,{emit:V}){const t=h,s=S(t,"open",V,{passive:!0}),w=C(),l=b(),c=b();function f(){c.value=void 0;try{t.state?l.value=JSON.parse(JSON.stringify(t.state||{})):(typeof t.state=="number"||typeof t.state!="string")&&(l.value=t.state)}catch(e){console.error(e),c.value=e}}O(()=>{f(),j(()=>[t.revision,t.state],e=>{typeof e!="number"&&typeof e!="string"?v(e,t.state):l.value=t.state},{deep:!0})});function v(e,r){const n=e[1];for(const o in n)Array.isArray(n[o])?r[o]=n[o].slice():typeof n[o]=="object"&&n[o]!==null?v(n[o],r[o]):r[o]=n[o]}async function B(){f(),await F()}return(e,r)=>{const n=_,o=M("tooltip");return i(),p("div",{class:"state-editor-details",open:e.name?a(s):!0},[d("div",U,[e.name?(i(),p("button",{key:0,flex:"~","cursor-pointer":"","items-center":"",class:u(a(s)?"":"op50"),onClick:r[0]||(r[0]=y=>s.value=!a(s))},[d("div",{"i-carbon-chevron-right":"",transition:"",class:u(a(s)?"rotate-90 op0":"")},null,2),d("code",{px3:"",py1:"","font-mono":"",class:u(a(s)?"bg-[#8881] rounded-t":"rounded hover:bg-active")},k(e.name),3)],2)):m("",!0),R(e.$slots,"actions",$(z({isOpen:a(s),name:e.name,state:e.state}))),a(s)?D((i(),g(n,{key:1,title:"Refresh View",icon:"carbon-renew",border:!1,onClick:B},null,512)),[[o,"Refresh View",void 0,{bottom:!0}]]):m("",!0)]),a(s)||!e.name?(i(),p(E,{key:0},[c.value?(i(),p("div",x," Error: "+k(c.value),1)):(i(),g(a(A),P({key:1,modelValue:l.value,"onUpdate:modelValue":r[1]||(r[1]=y=>l.value=y)},e.$attrs,{class:["json-editor-vue",[a(w)==="dark"?"jse-theme-dark":"",(e.name,"")]],"main-menu-bar":!1,"navigation-bar":!1,"status-bar":!1,"read-only":t.readonly,indentation:2,"tab-size":2}),null,16,["modelValue","class","read-only"]))],64)):m("",!0)],8,J)}}});export{Y as _};
