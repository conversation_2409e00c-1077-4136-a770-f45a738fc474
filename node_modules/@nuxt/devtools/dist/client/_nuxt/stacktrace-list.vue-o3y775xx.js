import{_ as m}from"./filepath-item.vue-n7veglgp.js";import{H as p}from"./d8ykqm74.js";import{k as f,S as r,F as s,ab as _,Q as n,U as o,a5 as h,O as d,W as k}from"./vendor/json-editor-vue-mdfcgt41.js";const g={mt2:"",grid:"~ cols-[max-content_1fr] gap-x-4","font-mono":""},x={"text-right":""},N={"ws-nowrap":""},B=f({__name:"StacktraceList",props:{stacktrace:{}},setup($){const i=p();function c(a){try{let t=new URL(a).pathname;return t.startsWith("/_nuxt/")&&(t=t.slice(6)),t.startsWith("/@id/virtual:nuxt:")?`#build/${t.split("/.nuxt/")[1]}`.replace(/\.m?js$/,""):t.includes("/@fs/")?`/${t.split("/@fs/")[1]}`:(i.value?.rootDir||"")+t}catch{return a}}return(a,t)=>{const l=m;return n(),r("div",g,[(n(!0),r(s,null,_(a.stacktrace,(e,u)=>(n(),r(s,{key:u},[o("div",x,h(e.functionName||"(anonymous)"),1),o("div",N,[e.fileName?(n(),d(l,{key:0,filepath:`${c(e.fileName)}:${e.lineNumber}:${e.columnNumber}`,subpath:""},null,8,["filepath"])):k("",!0)])],64))),128))])}}});export{B as _};
