# VSCode Language Server - Protocol Module

[![NPM Version](https://img.shields.io/npm/v/vscode-languageserver-protocol.svg)](https://npmjs.org/package/vscode-languageclient)
[![NPM Downloads](https://img.shields.io/npm/dm/vscode-languageserver-protocol.svg)](https://npmjs.org/package/vscode-languageclient)
[![Build Status](https://travis-ci.org/Microsoft/vscode-languageserver-node.svg?branch=master)](https://travis-ci.org/Microsoft/vscode-languageserver-node)

This npm module is a tool independent implementation of the language server protocol and can be used in any type of node application.

See [here](https://github.com/Microsoft/language-server-protocol) for a detailed documentation on the language server protocol.

## History

For the history please see the [main repository](https://github.com/Microsoft/vscode-languageserver-node/blob/master/README.md)

## License
[MIT](https://github.com/Microsoft/vscode-languageserver-node/blob/master/License.txt)
